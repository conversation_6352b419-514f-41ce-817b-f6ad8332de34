import time
from datetime import datetime
from enum import Enum
import pandas as pd

from rich import print
from sqlalchemy import create_engine
from src.config import load_settings
from src.extractors import (
    get_address_ids_from_universe,
    get_ireland_address_ids_from_universe,
    get_max_created_date_from_puddle,
    get_uk_address_ids_from_universe,
    today,
)
from src.helpers import clean_dataframe
from src.writers import (
    compile_universe_query,
    create_custom_question_response_df_from_df,
    run_universe_query,
    save_to_file,
)
from typer import Option, Typer, prompt
from typing_extensions import Annotated

app = Typer()

DONE_ADD_IDS = [4165, 1169, 4788, 4136, 3941, 4387, 4785, 2971, 4167, 3442]


class AddressScope(Enum):
    ALL = "all"
    IRELAND = "ireland"
    UK = "uk"

    def __str__(self):
        return self.value.capitalize()


@app.command()
def main(
    universe_db_host: str = "127.0.0.1",
    universe_db_port: int = 3308,
    universe_db_name: str = "universe_api",
    puddle_db_host: str = "127.0.0.1",
    puddle_db_port: int = 3307,
    puddle_db_name: str = "puddle",
    verbose: bool = True,
    test: Annotated[
        bool, Option("--test", "-t", help="Run in test mode")
    ] = False,  # noqa: E501
):
    start_time = time.time()

    try:
        if test:
            db_pass = "+yXqn4]whm$Vp}Mh"
        else:
            db_pass: str = prompt(
                "Enter password for both Universe & PuddleDB user-'reporting'",
                type=str,
                default="+yXqn4]whm$Vp}Mh",
                hide_input=True,
                show_default=False,
            )

        # Set/Load Configs
        settings = load_settings(
            db_pass,
            universe_db_host,
            universe_db_port,
            universe_db_name,
            puddle_db_host,
            puddle_db_port,
            puddle_db_name,
            verbose,
        )

        # Create SQL Engine
        puddle_sql_engine = create_engine(settings.puddle_db_conn_str)
        universe_sql_engine = create_engine(settings.universe_db_conn_str)

        max_created_date_prompt = prompt(
            "Enter the last updated date",
            type=str,
            default="2025-05-06 09:54:19",
        )

        if not max_created_date_prompt:
            # Check when was the last time updates were done
            max_created_date: datetime = get_max_created_date_from_puddle(
                puddle_sql_engine,
            )
        else:
            max_created_date = datetime.strptime(
                max_created_date_prompt, "%Y-%m-%d %H:%M:%S"
            )  # noqa
        print(
            "[bold blue]Data: [/bold blue] Max Created Date from Puddle: ",
            max_created_date,
        )

        # Skip and end if already done in latest
        if today <= max_created_date:
            print(
                "[bold green]Voila![/bold green]",
                "Everything is already to date.",
            )
            return

        use_csv = prompt(
            "Do you want to use a csv, instead of a fresh query?",
            type=bool,
            default=True,
        )
        if not use_csv:
            address_scope_choices = [scope.value for scope in AddressScope]
            text = "Which address scope do you want to query? \n"
            text += f"{address_scope_choices}"
            address_scope_str = prompt(
                text,
                type=str,
                default=AddressScope.ALL.value,
                show_choices=True,
            )
            try:
                address_scope = AddressScope(address_scope_str.lower())
            except ValueError:
                print(
                    "[bold red]Error: [/bold red]",
                    "Invalid address scope selected.",
                )
                return

            if address_scope == AddressScope.ALL:
                # Get AddressIDs from the Universe DB
                print(
                    "[bold green]Action: [/bold green]",
                    "Getting All Address IDs from Universe",
                )
                address_ids = get_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            elif address_scope == AddressScope.IRELAND:
                address_ids = get_ireland_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            elif address_scope == AddressScope.UK:
                address_ids = get_uk_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            else:
                raise ValueError(
                    "Invalid address scope selected. Please choose a valid option."  # noqa
                )

            address_ids = [
                address_id
                for address_id in address_ids
                if address_id not in DONE_ADD_IDS
            ]

            print(
                "[bold yellow]Action: [/bold yellow] Fetching Total Result Count:",  # noqa
                f" for addresses - {address_ids}",
            )

            # with Progress(
            #     SpinnerColumn(),
            #     TextColumn("[progress.description]{task.description}"),
            #     transient=True,
            # ) as progress:
            #     task = progress.add_task("Counting responses...", start=True)
            #     progress.start_task(task)
            #     total_response_count = get_total_responses_count(
            #         universe_sql_engine,
            #         max_created_date,
            #         address_ids,
            #     )[0][0]

            # print(
            #     "[bold blue]Data: [/bold blue] Total Result Count: ",
            #     total_response_count,
            # )

            address_batch_size = prompt(
                "Enter the batch size for processing (default is 4)",
                type=int,
                default=4,
            )

        if not use_csv:
            # Compile the batched query
            print(
                "[bold yellow]Action: [/bold yellow]Compiling the batched query"  # noqa
            )
            query_list = compile_universe_query(
                address_ids,
                max_created_date,
                batch=address_batch_size,
            )
            print("[bold blue]Data: [/bold blue] Query: ", query_list)

            execute_permission: bool = prompt(
                "Do you want to execute the query? (Y/N)",
                type=bool,
            )

            if not execute_permission:
                print(
                    "[bold red]Alert: [/bold red]",
                    "Query execution cancelled by user.",
                )
                return

            # Execute the query on Universe DB in batches
            print(
                "[bold yellow]Action: [/bold yellow]Executing the query on",
                "UniverseDB in Batches",
            )
            result = run_universe_query(
                universe_sql_engine,
                query_list,
            )
            print(
                "[bold blue]Data: [/bold blue]Unclean Result Shape: ",
                result.shape,
            )

            if result.empty:
                return

            # if result.shape[0] != total_response_count:
            #     raise Exception(
            #         f"Final Rows - {result.shape[0]} vs actual counts mismatch - {total_response_count}"  # noqa
            #     )

            # Clean the DataFrame
            print("[bold yellow]Action: [/bold yellow]Cleaning the DataFrame")
            result = clean_dataframe(result)
            print(
                "[bold blue]Data: [/bold blue] Result Shape: ",
                result.shape,
            )

            # Save the result to file (only CSV is supported for now)
            save_to_file_prompt = prompt(
                "Do you want to save the result to a file? (Y/N)",
                type=bool,
                default=True,
            )
            if save_to_file_prompt:
                default_file_path = "data/universe_query_result.csv"
                universe_query_filename = prompt(
                    "Enter the filename to save the query result",
                    type=str,
                    default=(
                        default_file_path if test else "file_path/file_name.csv"  # noqa
                    ),  # noqa: E501
                )
                save_to_file(result, universe_query_filename)

            print(
                "[bold yellow]Action: [/bold yellow]",
                "Beginning extraction of custom questions from response",
            )
        else:
            csv_path = prompt(
                "Enter the file path with .csv",
                type=str,
                default="data/universe_ireland.csv",
            )

            result = pd.read_csv(csv_path)

        surveyId = prompt(
            "Enter the surveyId to extract custom questions",
            type=int,
            default=350,
        )
        print(
            "[bold yellow]Action: [/bold yellow]",
            "Creating custom question response from DataFrame",
            f"for surveyId: {surveyId}",
        )

        filtered_response = create_custom_question_response_df_from_df(
            result,
            surveyId,
        )

        print(
            "[bold blue]Data: [/bold blue]",
            "Filtered Response: \n",
            filtered_response.head(),
        )

        # Check for Duplicates from the DB
        # and remove them from the df
        # print("[bold yellow]Action: [/bold yellow]Removing Duplicates")
        # final_result = remove_existing_data_in_db(
        #     puddle_sql_engine,
        #     filtered_response,
        #     "Custom_Question_Responses",
        # )
        print(
            "[bold blue]Data: [/bold blue]Result Shape: ",
            filtered_response.shape[0],
            # "[bold blue]Data: [/bold blue]Total Response Count: ",
            # total_response_count,
        )

        print(
            "[bold red]Alert: [/bold red]Irreversible, be careful !!",
        )
        upload_to_puddle = prompt(
            "Upload to Puddle DB?",
            type=bool,
            default=True,
        )
        if not upload_to_puddle:
            return

        if surveyId in [350, 330, 38]:
            filtered_response.to_sql(
                "Custom_Question_Responses",
                con=puddle_sql_engine,
                if_exists="append",
                index=False,
                chunksize=10,
            )
        elif surveyId == 83:
            filtered_response.to_sql(
                "PSS4_Universe_Responses",
                con=puddle_sql_engine,
                if_exists="append",
                index=False,
                chunksize=10,
            )

    except Exception as e:
        print("[bold red]Error: [/bold red]", e)
        elapsed_time = time.time() - start_time
        print("Operation finished, time taken: %.2f seconds" % (elapsed_time))
        return

    elapsed_time = time.time() - start_time
    print("Operation finished, time taken: %.2f seconds" % (elapsed_time))


if __name__ == "__main__":
    app()
