-- Delete AU Data
-- InfrastructureRegions
--   1 AU
--   2 UK
-- Countries
--   Not All Countries has InfrastrcutureRegion
--   country id 14 AU, 160 New Zealand

--
-- UK HealthStations
--      ("SISU000022", "SISU000116", "SISU000118", "SISU000120", "SISU000123", "SISU000124", "SISU000125", "SISU000128",
--       "SISU000132", "SISU000135", "SISU000138", "SISU000139", "SISU000140", "SISU000142", "SISU000143", "SISU000155",
--       "SISU000157", "SISU000158", "SISU000160", "SISU000164", "SISU000165", "SISU000166", "SISU000168", "SISU000169",
--       "SISU000170", "SISU000172", "SISU000173", "SISU000175", "SISU000209", "SISU000214", "SISU000225", "SISU000226",
--       "SISU000227", "SISU000228", "SISU000232", "SISU000236", "SISU000237", "SISU000238", "SISU000240", "SISU000242",
--       "SISU000243", "SISU000244", "SISU000245", "SISU000247", "SISU000248", "SISU000249", "SISU000325", "SISU000327",
--       "SISU000331", "SISU000332", "SISU000337", "SISU000338", "SISU000339", "SISU000353", "SISU000354", "SISU000355",
--       "SISU000357", "SISU000377", "SISU000385", "SISU000391", "SISU000392", "SISU000393", "SISU000394", "SISU000395",
--       "SISU000396", "SISU000397", "SISU000398", "SISU000399", "SISU000400", "SISU000401", "SISU000402", "SISU000403",
--       "SISU000404", "SISU000405", "SISU000406", "SISU000407", "SISU000408", "SISU000409", "SISU000485", "SISU000486",
--       "SISU000487", "SISU000488", "SISU000489", "SISU000490", "SISU000491", "SISU000492", "SISU000493", "SISU000494",
--       "SiSU000531", "SISU000532", "SISU000533", "SISU000534", "SISU000535", "SISU000536", "SISU000549", "SISU000550",
--       "SISU000551", "SISU000552", "SISU000553", "SISU000554", "SISU000568", "SISU000569", "SISU000570", "SISU000571",
--       "SISU000575", "SISU000576", "SISU000577", "SISU000578", "SISU000579", "SISU000580", "SISU000581", "SISU000582",
--       "SISU000583", "SISU000584", "SISU000591", "SISU000592", "SISU000593", "SISU000594", "SISU000595", "SISU000596",
--       "SISU000597", "SISU000598", "SISU000599", "SISU000600", "SISU000601", "SISU000602", "SISU000611", "SISU000612",
--       "SISU000613", "SISU000614", "SISU000615", "SISU000616", "SISU000617", "SISU000618", "SISU000625", "SISU000626",
--       "SISU000627", "SISU000628", "SISU000629", "SISU000630", "SISU000631", "SISU000632", "SISU000633", "SISU000634",
--       "SISU000645", "SISU000646", "SISU000647", "SISU000648", "SISU000649", "SISU000650", "SISU000651", "SISU000652",
--       "SISU000653", "SISU000654", "SISU000655", "SISU000656", "SISU000657", "SISU000658", "SISU000659", "SISU000660",
--       "SISU000661", "SISU000662", "SISU000663", "SISU000664", "SMIN000002", "SMIN000020", "SMIN000023", "SMIN000024",
--       "SMIN000025", "SMIN000033", "SMIN000034", "SMIN000035", "SMIN000040", "SMIN000041", "SMIN000042", "SMIN000043",
--       "SMIN000044", "SMIN000045", "SMIN000046", "SMIN000047", "SMIN000048", "SMIN000049", "SMIN000050", "SMIN000051", "SMIN000052",
--       "SMIN000044", "SMIN000045", "SMIN000053", "SMIN000054", "SMIN000055", "SMIN000056", "SMIN000057", "SMIN000058",
--       "SMIN000059", "SMIN000060")
-- 

--
-- Preparation for Users
--
create table tmp_AU_Users like Users;

insert into tmp_AU_Users select * from tmp_AU_Users;
-- Clean through batch_delete_UserRecommendations.sh
delete c from UserRecommendations c join tmp_AU_Users u using(userId);

-- CampaignAds
delete from CampaignAds where campaignadid in (select campaignadid from UserRecommendations) is not true; 
-- CampaignFeatures
delete from CampaignFeatures where campaignid in (select campaignid from CampaignAds) is not true and createdAt <= '2025-05-28 00:00:00';
-- CampaignHealthStations
delete from CampaignHealthStations where campaignid in (select campaignid from CampaignAds) is not true and healthStationId not in ( /* UK Station List*/);
-- ClientUserDataReportEntries
delete c from ClientUserDataReportEntries c join tmp_AU_Users u using(userId);
-- CampaignUserJourneys
delete from CampaignUserJourneys where campaignid not in (select campaignid from CampaignAds);
-- CampaignCoupons 
delete from CampaignCoupons where campaignid not in (select campaignid from CampaignAds);

-- AnalyticsHealthCheckUserConsents
-- batch_delete_AnalyticsHealthCheckUserConsents.sh
delete from AnalyticsHealthCheckUserConsents a join tmp_AU_Users u using(userid);

-- CustomerRequestEvents 
delete from CustomerRequestEvents where customerRequestId in (select customerRequestId from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where userid in (select userid from tmp_AU_Users)));

delete from CustomerRequestEvents where customerRequestId in (select customerRequestId from CustomerRequests cr join AnalyticsHealthChecks ahc using(analyticsHealthCheckId) where infrastructureRegionId=1);

-- PharmacyClinicalInterventionRecommendations
delete from PharmacyClinicalInterventionRecommendations;
-- PharmacyClinicalInterventions
delete from PharmacyClinicalInterventions;
-- CustomerRequestTriggers
delete from CustomerRequestTriggers where customerRequestId in (select customerRequestId from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where userid in (select userid from tmp_AU_Users)));

delete from CustomerRequestTriggers where customerRequestId in (select customerRequestId from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where infrastructureRegionId=1));

delete from CustomerRequestTriggers where customerRequestId in (select customerRequestId from CustomerRequests cr join AnalyticsHealthChecks ahc using(analyticsHealthCheckId) where infrastructureRegionId=1);
-- CustomerRequest
delete from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where userid in (select userid from tmp_AU_Users));  
delete from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where infrastructureRegionId=1);  

-- AnalyticsHealthCheckClientDepartments
delete from AnalyticsHealthCheckClientDepartments where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where infrastructureRegionId=1);

delete r from AnalyticsHealthCheckClientDepartments r join AnalyticsHealthChecks ahc using(analyticsHealthCheckId) where ahc.infrastructureRegionId=1;

delete a from AnalyticsHealthCheckClientDepartments a join AnalyticsHealthChecks ahc using(analyticsHealthCheckId) join Clients l using(clientId) where ahc.infrastructureRegionId=1 and l.countryId=14;

-- AnalyticsHealthChecks
-- delete Responses first
-- through batch_delete_AnalyticsHealthChecks.sh
delete from AnalyticsHealthChecks where userid in (select userid from tmp_AU_Users);
delete from AnalyticsHealthChecks where infrastructureRegionId=1;
-- ClientAddressUsers
-- May not delete
delete from ClientAddressUsers where userid in (select userid from Users where 
signupregion=1);
delete cu from ClientAddressUsers cu join ( select clientAddressId from ClientAddresses c join Addresses a using(addressId) join Clients cl using(clientId) where a.countryId=14 and cl.countryId=14) ca using(clientAddressId);
delete c from ClientAddressUsers c inner join tmp_AU_Users u using(userId);
-- ClientUsers
delete from ClientUsers where userid in (select userid from tmp_AU_Users);
delete c from ClientUsers c inner join tmp_AU_Users u using(userId);

-- Responses
-- delete AnalysableSurveys first
--   It will take long time to delete
-- the following will only delete Responses when user has registered

delete r from Responses r join tmp_AU_Users u on r.author = u.userId;

delete r from Responses r join Addresses a using(addressId) where a.countryId=14;

-- UserAuths
create table tmp_UserAuths like UserAuths;
insert into tmp_UserAuths select * from UserAuths;

delete r from UserAuths r inner join tmp_AU_Users u using(userId);

-- UserCampaignActivity
-- BIG Table
update Users set createdByUserCampaignActivityId=null where signupregion=1; 
delete from UserCampaignActivity where userId in (select userId from tmp_AU_Users);

delete r from UserCampaignActivity r inner join tmp_AU_Users u using(userId);
-- AnalyticsCitizens
delete r from AnalyticsCitizens r join tmp_AU_Users u using(userId);

-- UserCitizenLedger
delete r from UserCitizenLedger r join tmp_AU_Users u using(userId);
-- UserCitizens 
delete r from UserCitizens r join tmp_AU_Users u using(userId);

-- UserSubscriptions
delete r from UserSubscriptions r join tmp_AU_Users u using(userId);

-- UserConsentLedger
-- through script batch_delete_UserConsentLedger.sh
delete r from UserCitizenLedger r join tmp_AU_Users u using(userId)
-- UserIds
create table tmp_UserIds like UserIds;
insert into tmp_UserIds select * from UserIds;

delete r from UserIds r inner join tmp_AU_Users u using(userId);

-- UserRoles
create table tmp_UserRoles like UserRoles;
insert into tmp_UserRoles select * from UserRoles;

delete r from UserRoles r inner join tmp_AU_Users u using(userId);

-- DoctorsOnDemandQuickScripts
delete from DoctorsOnDemandQuickScripts;

-- DoctorsOnDemandPatients
delete from DoctorsOnDemandPatients;
-- ScriptsNowTransactions
delete from ScriptsNowTransactions;
-- StripeCustomerDetails
delete from StripeCustomerDetails
-- Users
delete from Users where signupRegion=1;
-- CustomerRequestEvents
delete from CustomerRequestEvents where customerRequestId in (select customerRequestId from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where infrastructureRegionId=1));
-- CustomerRequest
delete from CustomerRequests where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where infrastructureRegionId=1);
-- AnalyticsHealthCheckUserConsents
delete r from AnalyticsHealthCheckUserConsents r join AnalyticsHealthChecks ahc using(analyticsHealthCheckId) where ahc.infrastructureRegionId=1;
-- AnalyticsHealthChecks
-- through  batch_delete_AnalyticsHealthChecks.sh
delete from AnalyticsHealthChecks where infrastructureRegionId=1;
-- ClientUserDataReportEntries
delete from ClientUserDataReportEntries where addressId in (select addressId from Addresses where countryid=14); 
-- HealthStationInstallations
delete from HealthStationInstallations where healthStationId in (/*UK Station Id List*/);

-- UserRecommendations
delete r from UserRecommendations r join tmp_AU_Users u using(userId);

-- CampaignAds
-- Not Delete


-- CampaignFeatures
-- Dont Delete

-- UserCampaignActivity
-- big table
-- 
-- Users
update Users set createdByUserCampaignActivityId=null where userid in (select distinct userid from UserCampaignActivity where campaignid in (select campaignid from Campaigns where clientid in (select distinct clientid from ClientAddresses where addressid in (select addressid from Addresses where countryid = 14))));


delete  r from UserCampaignActivity r join tmp_AU_Users u using(userId);

-- CampaignHealthStations
delete from CampaignHealthStations where campaignid in (select campaignid from Campaigns where clientid in (select clientid from Clients where countryid = 14));

delete from CampaignHealthStations where campaignid in (select campaignid from Campaigns where clientid in (select distinct clientid from ClientAddresses where addressid in (select addressid from Addresses where countryid = 14)));
-- CampaignCoupons
delete from CampaignCoupons where campaignid in (select campaignid from Campaigns where clientid in (select clientid from Clients where countryid = 14));
delete from CampaignCoupons where campaignid in (select campaignid from Campaigns where clientid in (select distinct clientid from ClientAddresses where addressid in (select addressid from Addresses where countryid = 14)));
-- CampaignUserJourneys
delete from CampaignUserJourneys where campaignid in (select campaignid from Campaigns where clientid in (select clientid from Clients where countryid = 14));
-- Campaigns
delete from Campaigns where clientid in (select clientid from Clients where countryid = 14);
-- ClientAddressStaff
delete from ClientAddressStaff where clientaddressid in (select clientAddressId from ClientAddresses where clientid in (select clientid from Clients where countryid = 14));
-- ClientAddresses
delete from ClientAddresses where clientid in (select clientid from Clients where countryid = 14);
-- ClientUserDataReportEntries
delete from ClientUserDataReportEntries where clientUserDataReportId in (select clientUserDataReportId from ClientUserDataReports where clientid in (select clientid from Clients where countryid = 14));
-- ClientUserDataReports
delete from ClientUserDataReports where clientid in (select clientid from Clients where countryid = 14);
-- ClientUsers
delete from ClientUsers where clientid in (select clientid from Clients where countryid = 14);
-- UserSubscriptions
delete from UserSubscriptions where consentid in (select consentid from Consents where clientid in (select clientid from Clients where countryid = 14));
-- AnalyticsHealthCheckUserConsents
delete from AnalyticsHealthCheckUserConsents where consentid in (select consentid from Consents where clientid in (select clientid from Clients where countryid = 14));
-- Consents
delete from Consents where clientid in (select clientid from Clients where countryid = 14);
-- AnalyticsHealthCheckUserConsents
delete from AnalyticsHealthCheckUserConsents where analyticsHealthCheckId in (select analyticsHealthCheckId from AnalyticsHealthChecks where clientid in (select clientid from Clients where countryid = 14));
-- AnalyticsHealthChecks
delete from AnalyticsHealthChecks where clientid in (select clientid from Clients where countryid = 14);
-- ClientDepartments
delete from ClientDepartments where clientid in (select clientid from Clients where countryid = 14);
-- Clients
-- NOT DELETE
-- delete from Clients where countryid = 14;
-- UserJourneyPages
delete from UserJourneyPages where userJourneyId in (select userJourneyId from UserJourneys where userjourneyid not in (select distinct userjourneyid from Campaigns));
-- SurveyResponses
delete from SurveyResponses where userJourneyId in (select userJourneyId from UserJourneys where userjourneyid not in (select distinct userjourneyid from Campaigns));
-- UserJourneys
delete from UserJourneys where userjourneyid not in (select distinct userjourneyid from Campaigns);
--
-- may not delete localeid related tables
-- SurveyQuestions
--   delete from SurveyQuestions where surveyQuestionGroupId in (select surveyQuestionGroupId from SurveyQuestionGroups where surveyid in (select surveyid from Surveys where localeid = 1));
-- SurveyQuestionGroups
--   delete from SurveyQuestionGroups where surveyid in (select surveyid from Surveys where localeid = 1);
-- SurveyScoreSchemas
--   delete from SurveyScoreSchemas where surveyid in (select surveyid from Surveys where localeid = 1);
-- ClientUserDataReports
--   delete from ClientUserDataReports where consentid in (select consentid from Consents where surveyid in (select surveyid from Surveys where localeid = 1));
-- AnalyticsHealthCheckUserConsents
--   delete from AnalyticsHealthCheckUserConsents where consentid in (select consentid from Consents where surveyid in (select surveyid from Surveys where localeid = 1));
-- Consents
--   delete from Consents where surveyid in (select surveyid from Surveys where localeid = 1);
-- Surveys
--   delete from Surveys where localeid = 1;
--
-- ScriptsNowSessions
delete from ScriptsNowSessions;
-- TelstraHealthRequests
delete from TelstraHealthRequests;
-- PhenixRequests
delete from PhenixRequests;
-- PhenixStatusUpdates
delete from PhenixStatusUpdates;
-- Medications
delete from Medications;
-- MedicationsCategory
delete from MedicationsCategory;
-- MarketoJobs
delete from MarketoJobs;
--
-- LegacyHealthChecks
-- AnalyticsHealthRiskAssessmentLedger
-- AnalyticsHealthRiskAssessments
--   need to delete above before Users
-- HealthStationPingStats
TRUNCATE HealthStationPingStats;
-- HealthServiceProviders
delete from HealthServiceProviders;
-- AnalyticsHealthCheckLedger
delete from AnalyticsHealthCheckLedger where analyticshealthcheckid not in (select analyticshealthcheckid from AnalyticsHealthChecks);
-- AnalysableSurveys
delete from AnalysableSurveys where responseid in (select responseid from Responses r join Addresses a using(addressId) where a.countryId=14);

--
--  The Following Tables are not touched
--
-- AccountTypes
-- AccountUserRoles
-- AccountUsers
-- Accounts
-- AdSlots
-- Addresses
-- AnalyticsStatisticalSets
-- AppBuilds
-- AppVersions
-- Assets
-- CampaignActivityTypes
-- CustomerRequestEventTypes
-- CustomerRequestStates
-- CustomerRequestTypes
-- DataMappings
-- Features
-- Guidelines
-- HealthStationComponents
-- HealthStationStates
-- HealthStations
-- Industries
-- Locales
-- PharmacyClinicalInterventionClassifications
-- PharmacyRecommendations
-- Risks
-- Roles
-- RuleGroupRules
-- RuleGroups
-- RuleSetGroups
-- RuleSets
-- Rules
-- SequelizeMeta
-- SetValues
-- Sets
-- StripePaymentDetails
-- SurveyQuestionTypes
-- Tokens
-- UserJourneyPageTypes
-- UserStates
