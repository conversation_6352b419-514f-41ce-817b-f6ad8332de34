-- SELECT DISTINCT AnalyticsHealthChecks.addressId
-- FROM AnalyticsHealthChecks
--     JOIN Addresses ON AnalyticsHealthChecks.addressId = Addresses.addressId
--     JOIN Countries ON Addresses.countryId = Countries.countryId
-- WHERE date_created >= '2025-05-06 09:54:19'
--     AND date_created < '2025-06-23 14:20:00'
--     AND Addresses.countryId = 235
-- ORDER BY date_created DESC
-- LIMIT 1;

-- Increase sort buffer size for this session to help avoid out of sort memory error
SET SESSION sort_buffer_size = 4 * 1024 * 1024;

SELECT COUNT(1)
FROM universe_api.Responses r
JOIN ClientAddresses ca ON r.addressId = ca.addressId
WHERE JSON_EXTRACT(r.data, '$.configuration.campaign.clientId') = 1297;

-- SELECT count(1)
-- FROM universe_api.Responses
-- WHERE addressId IN (2971)
--     and createdAt between '2025-05-06 09:54:19' and '2025-06-25 00:00:00';