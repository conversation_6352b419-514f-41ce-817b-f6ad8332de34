from sqlalchemy import Engine, text
from typing import List
from datetime import datetime
import pandas as pd
from . import UK_COUNTRY_CODE, IE_COUNTRY_CODE
from .cache_manager import cached, query_cache

today = datetime.now()


@cached(ttl=300, use_disk=False)  # Cache for 5 minutes
def get_max_created_date_from_puddle(engine: Engine) -> datetime:
    """Get Max Created Date from Puddle DB

    Gets the last date when the custom question entry was added
    in a previous session.

    Keyword arguments:
    engine -- SQLAlchemy Engine

    Returns the max created date.
    """

    query = """
        SELECT MAX(createdDate) AS maxDateCreated
        FROM Custom_Question_Responses;
        """

    # Try to get from query cache first
    cached_result = query_cache.get_query_result(query, ttl=300)
    if cached_result is not None and not cached_result.empty:
        result = str(cached_result.values.flatten()[0])
        return datetime.fromisoformat(result)

    # Execute query and cache result
    result_df = pd.read_sql(query, engine)
    query_cache.cache_query_result(query, result_df)

    result = str(result_df.values.flatten()[0])
    result_date: datetime = datetime.fromisoformat(result)
    return result_date


@cached(ttl=600, use_disk=True)  # Cache for 10 minutes
def get_address_ids_from_universe(
    engine: Engine,
    last_updated_date_time: str,
    test: bool,
):
    # If test is true, the query will get only 1 result set
    query = f"""
        SELECT DISTINCT addressId
        FROM AnalyticsHealthChecks
        WHERE date_created >= '{last_updated_date_time}'
            AND date_created < '{today.strftime("%Y-%m-%d %H:%M:%S")}'
            AND infrastructureRegionId = 2
        ORDER BY date_created DESC{" LIMIT 10" if test else ""};
    """

    # Try to get from query cache first
    params = {"last_updated_date_time": last_updated_date_time, "test": test}
    cached_result = query_cache.get_query_result(query, params, ttl=600)
    if cached_result is not None and not cached_result.empty:
        return cached_result.values.flatten().tolist()

    # Execute query and cache result
    result_df = pd.read_sql(query, engine)
    query_cache.cache_query_result(query, result_df, params)

    return result_df.values.flatten().tolist()


def get_ireland_address_ids_from_universe(
    engine: Engine,
    last_updated_date_time: str,
    test: bool,
):
    # If test is true, the query will get only 1 result set
    query = f"""
        SELECT DISTINCT AnalyticsHealthChecks.addressId
        FROM AnalyticsHealthChecks
            JOIN Addresses ON AnalyticsHealthChecks.addressId = Addresses.addressId
            JOIN Countries ON Addresses.countryId = Countries.countryId
        WHERE date_created >= '{last_updated_date_time}'
            AND date_created < '{today.strftime("%Y-%m-%d %H:%M:%S")}'
            AND Addresses.countryId = {IE_COUNTRY_CODE}
        ORDER BY date_created DESC{" LIMIT 10" if test else ";"};
    """
    result = pd.read_sql(query, engine).values.flatten().tolist()
    return result


def get_uk_address_ids_from_universe(
    engine: Engine,
    last_updated_date_time: str,
    test: bool,
):
    # If test is true, the query will get only 1 result set
    query = f"""
        SELECT DISTINCT AnalyticsHealthChecks.addressId
        FROM AnalyticsHealthChecks
            JOIN Addresses ON AnalyticsHealthChecks.addressId = Addresses.addressId
            JOIN Countries ON Addresses.countryId = Countries.countryId
        WHERE date_created >= '{last_updated_date_time}'
            AND date_created < '{today.strftime("%Y-%m-%d %H:%M:%S")}'
            AND Addresses.countryId = {UK_COUNTRY_CODE}
        ORDER BY date_created DESC{" LIMIT 10" if test else ";"};
    """
    result = pd.read_sql(query, engine).values.flatten().tolist()
    return result


def create_address_criteria(address_ids: List[str]):
    return ", ".join([str(x) for x in address_ids])


def create_time_criteria(from_date_obj: datetime, to_date_obj: datetime):
    return " AND createdAt BETWEEN '%s' and '%s'" % (
        from_date_obj.strftime("%Y-%m-%d %H:%M:%S"),
        to_date_obj.strftime("%Y-%m-%d %H:%M:%S"),
    )


def get_total_responses_count(
    engine: Engine,
    max_created_date: datetime,
    address_ids: List[str],
):
    conn = engine.connect()
    time_criteria = create_time_criteria(max_created_date, today)
    address_criteria = create_address_criteria(address_ids)
    cursor = conn.execute(
        text(
            f"SELECT count(1) FROM universe_api.Responses WHERE addressId IN ({address_criteria}) {time_criteria};"  # noqa
        )
    )
    total_responses = cursor.fetchall()
    return total_responses
