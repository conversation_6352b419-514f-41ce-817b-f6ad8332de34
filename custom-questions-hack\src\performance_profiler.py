"""
Performance profiling and monitoring utilities.
This module provides comprehensive performance analysis tools.
"""

import cProfile
import io
import json
import pstats
import time
from contextlib import contextmanager
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, List

import numpy as np
import pandas as pd
import psutil


class PerformanceProfiler:
    """
    Comprehensive performance profiler for the custom questions script.
    """

    def __init__(self):
        self.metrics = {}
        self.memory_snapshots = []
        self.start_time = time.time()
        self.process = psutil.Process()

    def start_operation(self, operation_name: str):
        """Start timing an operation."""
        self.metrics[operation_name] = {
            "start_time": time.time(),
            "start_memory": self.get_memory_usage(),
            "start_cpu": self.process.cpu_percent(),
        }

    def end_operation(self, operation_name: str, additional_data: Dict = None):
        """End timing an operation and record metrics."""
        if operation_name not in self.metrics:
            return

        end_time = time.time()
        end_memory = self.get_memory_usage()
        end_cpu = self.process.cpu_percent()

        # Calculate memory delta properly
        start_memory = self.metrics[operation_name]["start_memory"]
        memory_delta = {
            "rss_mb": end_memory["rss_mb"] - start_memory["rss_mb"],
            "vms_mb": end_memory["vms_mb"] - start_memory["vms_mb"],
            "percent": end_memory["percent"] - start_memory["percent"],
        }

        self.metrics[operation_name].update(
            {
                "end_time": end_time,
                "duration": end_time
                - self.metrics[operation_name]["start_time"],  # noqa
                "end_memory": end_memory,
                "memory_delta": memory_delta,
                "end_cpu": end_cpu,
                "cpu_delta": end_cpu
                - self.metrics[operation_name]["start_cpu"],  # noqa
            }
        )

        if additional_data:
            self.metrics[operation_name].update(additional_data)

    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size
            "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            "percent": self.process.memory_percent(),
        }

    def take_memory_snapshot(self, label: str):
        """Take a memory snapshot with a label."""
        snapshot = {
            "timestamp": time.time(),
            "label": label,
            "memory": self.get_memory_usage(),
        }
        self.memory_snapshots.append(snapshot)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        total_time = time.time() - self.start_time

        summary = {
            "total_execution_time": round(total_time, 2),
            "operations": {},
            "memory_analysis": self.analyze_memory_usage(),
            "system_info": self.get_system_info(),
        }

        for operation, data in self.metrics.items():
            if "duration" in data:
                summary["operations"][operation] = {
                    "duration_seconds": round(data["duration"], 2),
                    "percentage_of_total": round(
                        (data["duration"] / total_time) * 100, 1
                    ),
                    "memory_delta_mb": round(
                        data.get("memory_delta", {}).get("rss_mb", 0), 2
                    ),
                    "cpu_usage_percent": round(data.get("cpu_delta", 0), 1),
                }

        return summary

    def analyze_memory_usage(self) -> Dict[str, Any]:
        """Analyze memory usage patterns."""
        if not self.memory_snapshots:
            return {}

        memory_values = [
            snapshot["memory"]["rss_mb"] for snapshot in self.memory_snapshots
        ]

        return {
            "peak_memory_mb": round(max(memory_values), 2),
            "min_memory_mb": round(min(memory_values), 2),
            "average_memory_mb": round(np.mean(memory_values), 2),
            "memory_growth_mb": round(memory_values[-1] - memory_values[0], 2),
            "snapshots_count": len(self.memory_snapshots),
        }

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(),
            "memory_total_gb": round(
                psutil.virtual_memory().total / 1024 / 1024 / 1024, 2
            ),
            "memory_available_gb": round(
                psutil.virtual_memory().available / 1024 / 1024 / 1024, 2
            ),
            "memory_percent": psutil.virtual_memory().percent,
        }

    def export_metrics(self, filename: str):
        """Export metrics to JSON file."""
        summary = self.get_performance_summary()
        summary["export_timestamp"] = datetime.now().isoformat()

        with open(filename, "w") as f:
            json.dump(summary, f, indent=2)

        print(
            f"[bold blue]Performance metrics exported to: [/bold blue] {filename}"  # noqa
        )


def profile_function(func: Callable) -> Callable:
    """
    Decorator to profile individual functions.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()

        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        profiler.disable()

        # Create string buffer to capture profiler output
        s = io.StringIO()
        ps = pstats.Stats(profiler, stream=s)
        ps.sort_stats("cumulative")
        ps.print_stats(10)  # Top 10 functions

        print(f"\n[bold yellow]Profile for {func.__name__}:[/bold yellow]")
        print(
            f"[bold blue]Execution time: [/bold blue] {end_time - start_time:.2f} seconds"  # noqa
        )
        print(f"[bold blue]Top functions:[/bold blue]")  # noqa
        print(s.getvalue())

        return result

    return wrapper


@contextmanager
def performance_context(
    profiler: PerformanceProfiler, operation_name: str, **kwargs
):  # noqa
    """
    Context manager for performance monitoring.
    """
    profiler.start_operation(operation_name)
    profiler.take_memory_snapshot(f"{operation_name}_start")

    try:
        yield
    finally:
        profiler.take_memory_snapshot(f"{operation_name}_end")
        profiler.end_operation(operation_name, kwargs)


class DataFrameProfiler:
    """
    Specialized profiler for DataFrame operations.
    """

    @staticmethod
    def profile_dataframe_operation(
        df: pd.DataFrame,
        operation_name: str,
        operation_func: Callable,
        *args,
        **kwargs,  # noqa
    ) -> tuple:
        """
        Profile a DataFrame operation and return result with metrics.
        """
        start_time = time.time()
        start_memory = df.memory_usage(deep=True).sum() / 1024 / 1024

        result = operation_func(df, *args, **kwargs)

        end_time = time.time()
        end_memory = (
            result.memory_usage(deep=True).sum() / 1024 / 1024
            if isinstance(result, pd.DataFrame)
            else 0
        )

        metrics = {
            "operation": operation_name,
            "duration": round(end_time - start_time, 2),
            "input_shape": df.shape,
            "output_shape": (
                result.shape if isinstance(result, pd.DataFrame) else "N/A"
            ),  # noqa
            "input_memory_mb": round(start_memory, 2),
            "output_memory_mb": round(end_memory, 2),
            "memory_delta_mb": round(end_memory - start_memory, 2),
        }

        print(f"[bold blue]DataFrame Operation Profile:[/bold blue]")  # noqa
        for key, value in metrics.items():
            print(f"  {key}: {value}")

        return result, metrics


class QueryProfiler:
    """
    Specialized profiler for database queries.
    """

    def __init__(self):
        self.query_metrics = []

    def profile_query(
        self, query: str, engine, params: Dict = None
    ) -> pd.DataFrame:  # noqa
        """
        Profile a database query execution.
        """
        start_time = time.time()

        try:
            if params:
                result = pd.read_sql(query, engine, params=params)
            else:
                result = pd.read_sql(query, engine)

            end_time = time.time()

            metrics = {
                "query_hash": hash(query),
                "execution_time": round(end_time - start_time, 2),
                "result_rows": len(result),
                "result_columns": len(result.columns),
                "result_memory_mb": round(
                    result.memory_usage(deep=True).sum() / 1024 / 1024, 2
                ),
                "timestamp": datetime.now().isoformat(),
            }

            self.query_metrics.append(metrics)

            print("[bold blue]Query Profile:[/bold blue]")
            print(f"  Execution time: {metrics['execution_time']} seconds")
            print(
                f"  Result shape: ({metrics['result_rows']}, {metrics['result_columns']})"  # noqa
            )
            print(f"  Memory usage: {metrics['result_memory_mb']} MB")

            return result

        except Exception as e:
            end_time = time.time()
            print(
                f"[bold red]Query failed after {end_time - start_time:.2f} seconds: [/bold red] {e}"  # noqa
            )
            raise

    def get_query_summary(self) -> Dict[str, Any]:
        """Get summary of all profiled queries."""
        if not self.query_metrics:
            return {}

        execution_times = [m["execution_time"] for m in self.query_metrics]
        memory_usage = [m["result_memory_mb"] for m in self.query_metrics]

        return {
            "total_queries": len(self.query_metrics),
            "total_execution_time": round(sum(execution_times), 2),
            "average_execution_time": round(np.mean(execution_times), 2),
            "slowest_query_time": round(max(execution_times), 2),
            "total_memory_mb": round(sum(memory_usage), 2),
            "average_memory_mb": round(np.mean(memory_usage), 2),
        }


def benchmark_functions(
    functions: List[tuple],
    iterations: int = 1,
) -> Dict[str, Dict]:
    """
    Benchmark multiple functions and compare their performance.

    Args:
        functions: List of tuples (function_name, function, args, kwargs)
        iterations: Number of iterations to run each function

    Returns:
        Dictionary with benchmark results
    """
    results = {}

    for func_name, func, args, kwargs in functions:
        times = []

        for _ in range(iterations):
            start_time = time.time()
            func(*args, **kwargs)
            end_time = time.time()
            times.append(end_time - start_time)

        results[func_name] = {
            "average_time": round(np.mean(times), 4),
            "min_time": round(min(times), 4),
            "max_time": round(max(times), 4),
            "std_dev": round(np.std(times), 4),
            "iterations": iterations,
        }

    # Sort by average time
    sorted_results = dict(
        sorted(results.items(), key=lambda x: x[1]["average_time"])
    )  # noqa

    print("[bold blue]Benchmark Results:[/bold blue]")
    for func_name, metrics in sorted_results.items():
        print(
            f"  {func_name}: {metrics['average_time']}s (±{metrics['std_dev']}s)"  # noqa
        )  # noqa

    return sorted_results
