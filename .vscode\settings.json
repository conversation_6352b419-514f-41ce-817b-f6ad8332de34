{"sqltools.connections": [{"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "localhost", "port": 3307, "driver": "MySQL", "name": "<PERSON><PERSON><PERSON>", "database": "puddle", "username": "reporting", "password": "+yXqn4]whm$Vp}Mh"}, {"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "localhost", "port": 3308, "driver": "MySQL", "name": "Universe API", "database": "universe_api", "username": "reporting", "password": "+yXqn4]whm$Vp}Mh"}]}