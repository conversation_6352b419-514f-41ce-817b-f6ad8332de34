from datetime import datetime
from typing import <PERSON>ple
from dateutil import tz
import pandas as pd
import json
import re
from rich import print


def convert_dates(from_date: str, to_date: str) -> Tuple[datetime, datetime]:
    from_date_obj = datetime.fromisoformat(from_date)
    to_date_obj = datetime.fromisoformat(to_date)
    return (from_date_obj, to_date_obj)


def validate_dates(from_date: str, to_date: str) -> Exception | None:
    if to_date and from_date:
        (from_date_obj, to_date_obj) = convert_dates(from_date, to_date)
        # Validate the dates
        if to_date_obj <= from_date_obj:
            return Exception("Error in dates, please enter correct dates")
    return None


def clean_text(txt):
    return txt.lstrip('"').rstrip('"')


def convert_utc_to_local(utc_input):
    from_zone = tz.gettz("UTC")
    to_zone = tz.gettz("GMT")

    if isinstance(utc_input, pd.Timestamp):
        utc_datetime = utc_input
        if utc_datetime.tzinfo is None:
            utc_datetime = utc_datetime.tz_localize(from_zone)
    elif isinstance(utc_input, str):
        utc_datetime = datetime.strptime(utc_input, "%Y-%m-%dT%H:%M:%S.%fZ")
        utc_datetime = utc_datetime.replace(tzinfo=from_zone)
    else:
        raise TypeError("Input must be a string or pandas.Timestamp")
    return utc_datetime.astimezone(to_zone)


def clean_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """Clean the DataFrame"""
    # Clean the triple quotes from the output columns -
    # healthStationName, countryId, campaignName, clientName
    # using the clean_text function
    print("[bold yellow]Action: [/bold yellow]Cleaning the output")
    for col in [
        "healthStationName",
        "countryId",
        "campaignName",
        "clientName",
    ]:
        if col in df.columns:
            df[col] = df[col].apply(clean_text)

    # Clean the createdAt column and convert to local timezone using the
    # convert_utc_to_local function. Local timezone is UK
    if "createdAt" in df.columns:
        print(
            "[bold yellow]Action: [/bold yellow]",
            "Converting createdAt to local timezone",
        )
        df["createdAt"] = df["createdAt"].apply(
            lambda x: x.replace('"', "").replace("'", "")
        )
        df["createdAt"] = pd.to_datetime(df["createdAt"])
        df["createdAt"] = df["createdAt"].dt.tz_convert("GMT")

    # Clean the questions column using autofix_json
    if "questions" in df.columns:
        print(
            "[bold yellow]Action: [/bold yellow]",
            "Cleaning questions column with autofix_json",
        )

        df["questions"] = df["questions"].apply(clean_text)

    return df


def autofix_json(json_str: str) -> dict | None:
    """
    Attempts to auto-fix common JSON formatting issues and parse the string.
    Returns the parsed JSON object or None if it cannot be fixed.
    Also fixes malformed multi-line strings in fields like helpText.
    """
    try:
        # Try parsing as is
        return json.loads(json_str)
    except json.JSONDecodeError:
        print("JSON parsing failed, attempting to auto-fix...")

    fixed = json_str.strip()
    fixed = fixed.replace("'", '"')
    # Remove trailing commas before } or ]
    fixed = re.sub(r",(\s*[}\]])", r"\1", fixed)
    # Fix multi-line strings in helpText fields (and similar):
    # replace newlines inside quoted values with a space
    fixed = re.sub(
        r'("helpText"\s*:\s*")((?:[^"\\]|\\.)*)"',
        lambda m: m.group(1) + re.sub(r"[\r\n]+", " ", m.group(2)) + '"',
        fixed,
        flags=re.MULTILINE,
    )

    # Remove excessive whitespace outside of quoted strings
    def clean_outside_quotes(s):
        result = []
        in_quotes = False
        for c in s:
            if c == '"':
                in_quotes = not in_quotes
            if not in_quotes and c in "\r\n\t":
                result.append(" ")
            else:
                result.append(c)
        return "".join(result)

    fixed = clean_outside_quotes(fixed)
    # Remove multiple spaces
    fixed = re.sub(r" {2,}", " ", fixed)

    try:
        return json.loads(fixed)
    except Exception:
        raise ValueError(
            "Failed to parse JSON after auto-fixing. Please check the input format."  # noqa
        )
