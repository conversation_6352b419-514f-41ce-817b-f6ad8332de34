"""
Caching mechanisms for improved performance.
This module provides various caching strategies to avoid redundant operations.
"""

import pickle
import hashlib
import os
import time
from typing import Any, Dict, List, Optional, Callable
from functools import wraps
import pandas as pd
from datetime import datetime, timedelta


class CacheManager:
    """
    A comprehensive cache manager with multiple caching strategies.
    """
    
    def __init__(self, cache_dir: str = "cache", default_ttl: int = 3600):
        self.cache_dir = cache_dir
        self.default_ttl = default_ttl
        self.memory_cache = {}
        self.cache_stats = {"hits": 0, "misses": 0}
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_key(self, key: str, *args, **kwargs) -> str:
        """Generate a unique cache key based on function name and arguments."""
        key_data = f"{key}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_file: str, ttl: int) -> bool:
        """Check if cache file is still valid based on TTL."""
        if not os.path.exists(cache_file):
            return False
        
        file_age = time.time() - os.path.getmtime(cache_file)
        return file_age < ttl
    
    def get_from_memory(self, key: str) -> Optional[Any]:
        """Get data from memory cache."""
        if key in self.memory_cache:
            data, timestamp, ttl = self.memory_cache[key]
            if time.time() - timestamp < ttl:
                self.cache_stats["hits"] += 1
                return data
            else:
                del self.memory_cache[key]
        
        self.cache_stats["misses"] += 1
        return None
    
    def set_in_memory(self, key: str, data: Any, ttl: int = None) -> None:
        """Store data in memory cache."""
        ttl = ttl or self.default_ttl
        self.memory_cache[key] = (data, time.time(), ttl)
    
    def get_from_disk(self, key: str, ttl: int = None) -> Optional[Any]:
        """Get data from disk cache."""
        ttl = ttl or self.default_ttl
        cache_file = os.path.join(self.cache_dir, f"{key}.pkl")
        
        if self._is_cache_valid(cache_file, ttl):
            try:
                with open(cache_file, 'rb') as f:
                    self.cache_stats["hits"] += 1
                    return pickle.load(f)
            except (pickle.PickleError, IOError):
                pass
        
        self.cache_stats["misses"] += 1
        return None
    
    def set_on_disk(self, key: str, data: Any) -> None:
        """Store data on disk cache."""
        cache_file = os.path.join(self.cache_dir, f"{key}.pkl")
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except (pickle.PickleError, IOError) as e:
            print(f"[bold red]Warning: [/bold red] Failed to cache data: {e}")
    
    def get(self, key: str, *args, ttl: int = None, use_disk: bool = True, **kwargs) -> Optional[Any]:
        """Get data from cache (memory first, then disk)."""
        cache_key = self._get_cache_key(key, *args, **kwargs)
        
        # Try memory cache first
        data = self.get_from_memory(cache_key)
        if data is not None:
            return data
        
        # Try disk cache if enabled
        if use_disk:
            data = self.get_from_disk(cache_key, ttl)
            if data is not None:
                # Store in memory for faster access
                self.set_in_memory(cache_key, data, ttl)
                return data
        
        return None
    
    def set(self, key: str, data: Any, *args, ttl: int = None, use_disk: bool = True, **kwargs) -> None:
        """Store data in cache."""
        cache_key = self._get_cache_key(key, *args, **kwargs)
        
        # Always store in memory
        self.set_in_memory(cache_key, data, ttl)
        
        # Store on disk if enabled
        if use_disk:
            self.set_on_disk(cache_key, data)
    
    def clear_memory(self) -> None:
        """Clear memory cache."""
        self.memory_cache.clear()
    
    def clear_disk(self) -> None:
        """Clear disk cache."""
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.pkl'):
                os.remove(os.path.join(self.cache_dir, filename))
    
    def clear_all(self) -> None:
        """Clear all caches."""
        self.clear_memory()
        self.clear_disk()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            "hits": self.cache_stats["hits"],
            "misses": self.cache_stats["misses"],
            "hit_rate": round(hit_rate * 100, 2),
            "memory_cache_size": len(self.memory_cache)
        }


# Global cache manager instance
cache_manager = CacheManager()


def cached(ttl: int = 3600, use_disk: bool = True, key_prefix: str = None):
    """
    Decorator for caching function results.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = key_prefix or func.__name__
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key, *args, ttl=ttl, use_disk=use_disk, **kwargs)
            if cached_result is not None:
                print(f"[bold green]Cache Hit: [/bold green] {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            print(f"[bold yellow]Cache Miss: [/bold yellow] {func.__name__}")
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, *args, ttl=ttl, use_disk=use_disk, **kwargs)
            
            return result
        return wrapper
    return decorator


class DataFrameCache:
    """
    Specialized cache for DataFrame operations.
    """
    
    def __init__(self, cache_dir: str = "df_cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_df_hash(self, df: pd.DataFrame) -> str:
        """Generate hash for DataFrame based on content."""
        return hashlib.md5(pd.util.hash_pandas_object(df).values.tobytes()).hexdigest()
    
    def cache_dataframe(self, df: pd.DataFrame, key: str) -> str:
        """Cache DataFrame to disk and return cache key."""
        df_hash = self._get_df_hash(df)
        cache_key = f"{key}_{df_hash}"
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.parquet")
        
        if not os.path.exists(cache_file):
            df.to_parquet(cache_file, compression='snappy')
        
        return cache_key
    
    def get_dataframe(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Retrieve DataFrame from cache."""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.parquet")
        
        if os.path.exists(cache_file):
            try:
                return pd.read_parquet(cache_file)
            except Exception as e:
                print(f"[bold red]Warning: [/bold red] Failed to load cached DataFrame: {e}")
        
        return None


class QueryCache:
    """
    Specialized cache for database query results.
    """
    
    def __init__(self, cache_dir: str = "query_cache", default_ttl: int = 1800):
        self.cache_dir = cache_dir
        self.default_ttl = default_ttl
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_query_hash(self, query: str, params: Dict = None) -> str:
        """Generate hash for query and parameters."""
        query_data = f"{query}_{str(params or {})}"
        return hashlib.md5(query_data.encode()).hexdigest()
    
    def cache_query_result(self, query: str, result: pd.DataFrame, params: Dict = None) -> None:
        """Cache query result."""
        query_hash = self._get_query_hash(query, params)
        cache_file = os.path.join(self.cache_dir, f"{query_hash}.parquet")
        
        try:
            # Store result with metadata
            metadata = {
                'timestamp': time.time(),
                'query': query,
                'params': params or {},
                'row_count': len(result)
            }
            
            result.to_parquet(cache_file, compression='snappy')
            
            # Store metadata separately
            metadata_file = os.path.join(self.cache_dir, f"{query_hash}_meta.pkl")
            with open(metadata_file, 'wb') as f:
                pickle.dump(metadata, f)
                
        except Exception as e:
            print(f"[bold red]Warning: [/bold red] Failed to cache query result: {e}")
    
    def get_query_result(self, query: str, params: Dict = None, ttl: int = None) -> Optional[pd.DataFrame]:
        """Get cached query result if valid."""
        ttl = ttl or self.default_ttl
        query_hash = self._get_query_hash(query, params)
        cache_file = os.path.join(self.cache_dir, f"{query_hash}.parquet")
        metadata_file = os.path.join(self.cache_dir, f"{query_hash}_meta.pkl")
        
        if not (os.path.exists(cache_file) and os.path.exists(metadata_file)):
            return None
        
        try:
            # Check if cache is still valid
            with open(metadata_file, 'rb') as f:
                metadata = pickle.load(f)
            
            if time.time() - metadata['timestamp'] > ttl:
                return None
            
            # Load and return cached result
            return pd.read_parquet(cache_file)
            
        except Exception as e:
            print(f"[bold red]Warning: [/bold red] Failed to load cached query result: {e}")
            return None


# Global instances
df_cache = DataFrameCache()
query_cache = QueryCache()
