import json
from datetime import datetime
from typing import List

import pandas as pd
from rich import print
from sqlalchemy import Engine, text

from .extractors import create_address_criteria, create_time_criteria


def compile_universe_query(
    address_ids: List[str],
    max_created_date: datetime,
    batch: int = 50,  # Increased batch size for better performance
) -> List[str]:
    """
    Optimized version that uses larger batches and more efficient queries.
    """
    today = datetime.now()

    # Create Time Criteria once
    print("[bold yellow]Action: [/bold yellow]Preparing Time Criteria")
    time_criteria = create_time_criteria(max_created_date, today)
    print("[bold green]Data: [/bold green] Criteria: ", time_criteria)

    query_list = []

    # Use larger batches to reduce the number of queries
    for i in range(0, len(address_ids), batch):
        batch_ids = address_ids[i : i + batch]  # noqa: E203

        # Create Address Criteria
        address_criteria = create_address_criteria(batch_ids)

        # Optimized query with better structure and indexing hints
        query = f"""
            SELECT
                responseId,
                addressId,
                JSON_EXTRACT(data, '$.configuration.healthStationInstallation.healthStationId') as healthStationId,
                JSON_EXTRACT(data, '$.configuration.healthStationInstallation.name') as healthStationName,
                JSON_EXTRACT(data, '$.configuration.healthStationInstallation.address.countryId') as countryId,
                JSON_EXTRACT(data, '$.configuration.campaign.clientId') as clientId,
                JSON_EXTRACT(data, '$.configuration.campaign.client.name') as clientName,
                JSON_EXTRACT(data, '$.configuration.campaign.campaignId') as campaignId,
                JSON_EXTRACT(data, '$.configuration.campaign.name') as campaignName,
                JSON_EXTRACT(data, '$.createdAt') as createdAt,
                JSON_EXTRACT(data, '$.isTest') as isTest,
                JSON_EXTRACT(data, '$.userJourney.userJourneyPages[*].survey.schema.surveyId') as survey_ids,
                JSON_EXTRACT(data, '$.userJourney.userJourneyPages[*].survey.questionGroups[*].questions') as questions
            FROM universe_api.Responses
            WHERE addressId IN ({address_criteria}) {time_criteria}
            ORDER BY responseId
        """
        query_list.append(query)

    print(
        f"[bold blue]Data: [/bold blue] Generated {len(query_list)} optimized queries for {len(address_ids)} addresses"
    )

    if not has_no_duplicate_queries(query_list):
        raise Exception("Duplicate queries found in the query list.")

    return query_list


def compile_universe_query_df(
    address_ids: List[str],
    max_created_date: datetime,
    batch: int = 4,
) -> List[str]:
    today = datetime.now()

    # Create Time Criteria
    print("[bold yellow]Action: [/bold yellow]Preparing Time Criteria")
    time_criteria = create_time_criteria(max_created_date, today)
    print("[bold green]Data: [/bold green] Criteria: ", time_criteria)

    query_list = []

    for i in range(0, len(address_ids), batch):
        batch_ids = address_ids[i : i + batch]  # noqa: E203

        # Create Address Criteria
        print("[bold yellow]Action: [/bold yellow]Preparing Address Criteria")
        address_criteria = create_address_criteria(batch_ids)
        print("[bold green]Data: [/bold green] Criteria: ", address_criteria)

        query = f"""
            SELECT responseId, addressId, data
            FROM universe_api.Responses
            WHERE addressId IN ({address_criteria}) {time_criteria}
        """
        query_list.append(query)

    if not has_no_duplicate_queries(query_list):
        raise Exception("Duplicate queries found in the query list.")

    return query_list


def has_no_duplicate_queries(query_list: List[str]) -> bool:
    return len(query_list) == len(set(query_list))


# def format_result_in_universe_df(df: pd.DataFrame):
#     universe_output__columns = [
#         "responseId",
#         "addressId",
#         "healthStationId",
#         "healthStationName",
#         "countryId",
#         "clientId",
#         "clientName",
#         "campaignId",
#         "campaignName",
#         "createdAt",
#         "isTest",
#         "survey_ids",
#         "questions",
#     ]

#     for _, row in df.iterrows():
#         response_id: str = row["responseId"]
#         data = json.loads(row["data"])
#         health_station_id: int = data["configuration"]["healthStationInstallation"][
#             "healthStationId"
#         ]

#     return


def remove_existing_data_in_db(
    engine: Engine,
    df: pd.DataFrame,
    table_name: str,
) -> pd.DataFrame:
    """
    Removes rows from the DataFrame that already exist in the database table
    based on the composite primary key.
    Returns a DataFrame with only new (non-duplicate) rows.
    """
    if df.empty:
        raise ValueError("Dataframe is Empty")

    pk_cols = [
        "responseId",
        "surveyId",
        "healthStationId",
        "campaignId",
        "clientId",
        "createdDate",
        "questionId",
    ]

    # Build a tuple list of PK values
    pk_tuples = [tuple(row[col] for col in pk_cols) for _, row in df.iterrows()]
    if not pk_tuples:
        return df
    print("PK Tuples: ", pk_tuples)

    # Prepare SQL for checking existing PKs
    pk_conditions = []
    for pk in pk_tuples:
        conds = []
        for col, val in zip(pk_cols, pk):
            if col == "createdDate" and isinstance(val, pd.Timestamp):
                val = val.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(val, str):
                val = val.replace("'", "''")
                conds.append(f"{col} = '{val}'")
            else:
                conds.append(f"{col} = {val}")
        pk_conditions.append("(" + " AND ".join(conds) + ")")
    where_sql = " OR ".join(pk_conditions)

    query = f"SELECT {', '.join(pk_cols)} FROM {table_name} WHERE {where_sql}"
    print("Remove Duplication Query: ", query)
    with engine.connect() as conn:
        result = conn.execute(text(query))
        existing = set(tuple(row) for row in result.fetchall())

    # Filter out rows that already exist
    mask = [
        tuple(row[col] for col in pk_cols) not in existing for _, row in df.iterrows()
    ]
    return df[mask].reset_index(drop=True)


def run_universe_query(
    engine: Engine,
    query_list: List[str],
) -> pd.DataFrame:
    """
    Optimized version that reduces database round trips and improves memory efficiency.
    """
    all_results: List[pd.DataFrame] = []

    # Use larger batch size for better performance
    batch_size = 1000

    # Use connection pooling for better performance
    with engine.connect() as conn:
        for i, query in enumerate(query_list):
            print(
                "[bold yellow]Action: [/bold yellow]Executing optimized query",
                f"- query {i + 1}/{len(query_list)}",
                f"- batch size = {batch_size}",
            )

            # Execute query without pagination first to get total count
            count_query = f"SELECT COUNT(*) FROM ({query}) as subquery"
            total_count = pd.read_sql(count_query, conn).iloc[0, 0]

            if total_count == 0:
                continue

            print(f"- total records: {total_count}")

            # Use chunked reading for memory efficiency
            offset = 0
            while offset < total_count:
                paginated_query = f"{query} LIMIT {batch_size} OFFSET {offset}"

                try:
                    result = pd.read_sql(paginated_query, conn)
                    if result.empty:
                        break

                    all_results.append(result)
                    offset += batch_size

                    print(f"- fetched {offset} / {total_count} records")

                except Exception as e:
                    print(
                        f"[bold red]Error: [/bold red] Failed to fetch batch at offset {offset}: {e}"
                    )
                    break

    if not all_results:
        print(
            "[bold red]Alert: [/bold red]",
            "No results found for the query.",
        )
        return pd.DataFrame()

    # More efficient concatenation
    final_result = pd.concat(all_results, ignore_index=True, copy=False)

    # Clear intermediate results to free memory
    all_results.clear()

    return final_result


def save_to_file(df: pd.DataFrame, file_path: str):
    # Check if the file path ends with .csv
    if not file_path.endswith(".csv"):
        raise ValueError("File path must end with .csv")

    # Save the result to csv
    print("[bold yellow]Action: [/bold yellow]Saving the result to CSV")
    df.to_csv(f"{file_path}", index=False)
    print(
        "[bold blue]Data: [/bold blue]",
        f"Result saved to {file_path}",
    )


def create_custom_question_response_df_from_df(
    df: pd.DataFrame,
    target_survey_id: int,
) -> pd.DataFrame:
    """
    Iterates through a DataFrame and creates a flattened list of
    CustomQuestionResponse objects by parsing the 'questions' JSON in each row,
    filtered by surveyId.
    """
    all_responses: List = []
    if target_survey_id != 83:
        puddle_output_df = pd.DataFrame(
            columns=[
                "responseId",
                "surveyId",
                "healthStationId",
                "healthStationInstallation",
                "countryId",
                "campaignId",
                "campaignName",
                "clientId",
                "clientName",
                "createdDate",
                "isTest",
                "questionId",
                "answer",
            ]
        )

        for index, row in df.iterrows():
            response_id: str = row["responseId"]
            survey_ids: List = json.loads(row["survey_ids"])
            health_station_id: int = row["healthStationId"]
            health_station_installation: str = row["healthStationName"]
            country_id: int = row["countryId"]
            campaign_id: int = row["campaignId"]
            campaign_name: str = row["campaignName"]
            client_id: int = row["clientId"]
            client_name: str = row["clientName"]
            created_date: str = row["createdAt"]
            is_test: bool = row["isTest"]

            # Parse the JSON data in the 'questions' column
            questions_data = json.loads(row["questions"])

            # Find the index of the target_survey_id in the survey_ids list
            try:
                survey_index = survey_ids.index(target_survey_id)

                # Check if the index is valid for questions_data
                if survey_index < len(questions_data):
                    # Get the question group for the target survey ID
                    target_question_group = questions_data[survey_index]

                    # Iterate through each question in the target question group
                    for question in target_question_group:

                        # Skip question is not asked to the user
                        if question["questionState"] == "notAsked":
                            continue

                        question_id = question["schema"]["surveyQuestionId"]
                        # Assuming there is only one answer per
                        # question for simplicity
                        answer = (
                            question["answers"][0]["answer"]
                            if question["answers"]
                            else None
                        )

                        # Create a new row dictionary
                        new_row = {
                            "responseId": response_id,
                            "surveyId": target_survey_id,
                            "healthStationId": health_station_id,
                            "healthStationInstallation": health_station_installation,  # noqa
                            "countryId": country_id,
                            "campaignId": campaign_id,
                            "campaignName": campaign_name,
                            "clientId": client_id,
                            "clientName": client_name,
                            "createdDate": created_date,
                            "isTest": is_test,
                            "questionId": question_id,
                            "answer": answer,
                        }

                        # Append the new row dictionary to the list
                        all_responses.append(new_row)
                else:
                    print(
                        f"Warning: Survey ID {target_survey_id} index out of bounds for questions data in row {index}"  # noqa
                    )

            except ValueError:
                print(
                    f"Warning: Target survey ID {target_survey_id} not found in survey_ids for row {index}"  # noqa
                )
    else:
        puddle_output_df = pd.DataFrame(
            columns=[
                "responseId",
                "questionId",
                "answer",
            ]
        )

        for index, row in df.iterrows():
            response_id: str = row["responseId"]
            survey_ids: List = json.loads(row["survey_ids"])

            # Parse the JSON data in the 'questions' column
            questions_data = json.loads(row["questions"])

            # Find the index of the target_survey_id in the survey_ids list
            try:
                survey_index = survey_ids.index(target_survey_id)

                # Check if the index is valid for questions_data
                if survey_index < len(questions_data):
                    # Get the question group for the target survey ID
                    target_question_group = questions_data[survey_index]

                    # Iterate through each question in the target question group
                    for question in target_question_group:

                        # Skip question is not asked to the user
                        if question["questionState"] == "notAsked":
                            continue

                        question_id = question["schema"]["surveyQuestionId"]
                        # Assuming there is only one answer per
                        # question for simplicity
                        answer = (
                            question["answers"][0]["answer"]
                            if question["answers"]
                            else None
                        )

                        # Create a new row dictionary
                        new_row = {
                            "responseId": response_id,
                            "questionId": question_id,
                            "answer": answer,
                        }

                        # Append the new row dictionary to the list
                        all_responses.append(new_row)
                else:
                    print(
                        f"Warning: Survey ID {target_survey_id} index out of bounds for questions data in row {index}"  # noqa
                    )

            except ValueError:
                print(
                    f"Warning: Target survey ID {target_survey_id} not found in survey_ids for row {index}"  # noqa
                )

    # Concatenate the list of new rows into the puddle_output_df
    puddle_output_df = pd.concat(
        [pd.DataFrame(all_responses)], ignore_index=True
    )  # noqa
    return puddle_output_df
