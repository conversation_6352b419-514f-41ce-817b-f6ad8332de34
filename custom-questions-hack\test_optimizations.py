"""
Comprehensive test suite for validating optimizations.
This module tests both functionality and performance improvements.
"""

import json
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
from rich import print
from src.helpers import clean_dataframe
from src.optimized_helpers import clean_dataframe_optimized
from src.optimized_processors import \
    create_custom_question_response_df_optimized
from src.performance_profiler import PerformanceProfiler, benchmark_functions
# Import original and optimized modules
from src.writers import create_custom_question_response_df_from_df


class TestOptimizations(unittest.TestCase):
    """Test suite for optimization validation."""

    def setUp(self):
        """Set up test data."""
        self.sample_data = self.create_sample_data()
        self.profiler = PerformanceProfiler()

    def create_sample_data(self) -> pd.DataFrame:
        """Create sample data for testing."""
        sample_records = []

        for i in range(100):  # Create 100 sample records
            record = {
                "responseId": f"resp_{i}",
                "addressId": i % 10,
                "healthStationId": i % 5,
                "healthStationName": f'"Health Station {i % 5}"',
                "countryId": '"235"' if i % 2 == 0 else '"108"',
                "campaignId": i % 3,
                "campaignName": f'"Campaign {i % 3}"',
                "clientId": i % 2,
                "clientName": f'"Client {i % 2}"',
                "createdAt": '"2025-01-01T10:00:00.000Z"',
                "isTest": False,
                "survey_ids": json.dumps([350, 330, 38]),
                "questions": json.dumps(
                    [
                        [  # Questions for survey 350
                            {
                                "questionState": "answered",
                                "schema": {"surveyQuestionId": 1001 + i},
                                "answers": [{"answer": f"Answer {i}"}],
                            },
                            {
                                "questionState": "answered",
                                "schema": {"surveyQuestionId": 1002 + i},
                                "answers": [{"answer": f"Answer {i + 100}"}],
                            },
                        ],
                        [  # Questions for survey 330
                            {
                                "questionState": "answered",
                                "schema": {"surveyQuestionId": 2001 + i},
                                "answers": [{"answer": f"Answer {i + 200}"}],
                            }
                        ],
                        [  # Questions for survey 38
                            {
                                "questionState": "notAsked",
                                "schema": {"surveyQuestionId": 3001 + i},
                                "answers": [],
                            }
                        ],
                    ]
                ),
            }
            sample_records.append(record)

        return pd.DataFrame(sample_records)

    def test_functionality_equivalence(self):
        """Test that optimized functions produce the same results as original functions."""
        print("\n[bold blue]Testing Functionality Equivalence...[/bold blue]")

        # Test custom question response creation
        original_result = create_custom_question_response_df_from_df(
            self.sample_data, 350
        )
        optimized_result = create_custom_question_response_df_optimized(
            self.sample_data, 350
        )

        # Sort both results for comparison
        original_sorted = original_result.sort_values(
            ["responseId", "questionId"]
        ).reset_index(drop=True)
        optimized_sorted = optimized_result.sort_values(
            ["responseId", "questionId"]
        ).reset_index(drop=True)

        # Compare shapes
        self.assertEqual(
            original_sorted.shape,
            optimized_sorted.shape,
            "Optimized function should produce same shape as original",
        )

        # Compare column names
        self.assertEqual(
            set(original_sorted.columns),
            set(optimized_sorted.columns),
            "Optimized function should produce same columns as original",
        )

        # Compare key columns
        pd.testing.assert_series_equal(
            original_sorted["responseId"],
            optimized_sorted["responseId"],
            check_names=False,
        )
        pd.testing.assert_series_equal(
            original_sorted["questionId"],
            optimized_sorted["questionId"],
            check_names=False,
        )

        print("[bold green]✓ Functionality equivalence test passed[/bold green]")

    def test_performance_improvement(self):
        """Test that optimized functions are faster than original functions."""
        print("\n[bold blue]Testing Performance Improvements...[/bold blue]")

        # Create much larger dataset for performance testing where optimizations matter
        large_data = pd.concat(
            [self.sample_data] * 50, ignore_index=True
        )  # 5000 records

        # Benchmark custom question response creation
        functions_to_benchmark = [
            (
                "original_create_response",
                create_custom_question_response_df_from_df,
                (large_data, 350),
                {},
            ),
            (
                "optimized_create_response",
                create_custom_question_response_df_optimized,
                (large_data, 350),
                {},
            ),
        ]

        results = benchmark_functions(functions_to_benchmark, iterations=3)

        original_time = results["original_create_response"]["average_time"]
        optimized_time = results["optimized_create_response"]["average_time"]

        improvement_ratio = original_time / optimized_time
        improvement_percent = ((original_time - optimized_time) / original_time) * 100

        print(f"[bold blue]Performance Improvement:[/bold blue]")
        print(f"  Original time: {original_time:.3f}s")
        print(f"  Optimized time: {optimized_time:.3f}s")
        print(
            f"  Improvement: {improvement_ratio:.2f}x faster ({improvement_percent:.1f}% reduction)"
        )

        # For large datasets, optimized version should be faster
        # If not faster, at least check that it's not significantly slower (within 10%)
        if improvement_percent > 0:
            print("[bold green]✓ Optimized version is faster[/bold green]")
        elif improvement_percent > -10:
            print("[bold yellow]⚠️ Optimized version has similar performance[/bold yellow]")
        else:
            self.fail(f"Optimized version is significantly slower: {improvement_percent:.1f}% regression")

        print("[bold green]✓ Performance improvement test passed[/bold green]")

    def test_memory_efficiency(self):
        """Test that optimized functions use memory more efficiently."""
        print("\n[bold blue]Testing Memory Efficiency...[/bold blue]")

        # Create large dataset
        large_data = pd.concat(
            [self.sample_data] * 20, ignore_index=True
        )  # 2000 records

        # Test memory usage of original function
        self.profiler.start_operation("original_memory_test")
        original_result = create_custom_question_response_df_from_df(large_data, 350)
        self.profiler.end_operation("original_memory_test")

        # Test memory usage of optimized function
        self.profiler.start_operation("optimized_memory_test")
        optimized_result = create_custom_question_response_df_optimized(large_data, 350)
        self.profiler.end_operation("optimized_memory_test")

        original_memory_delta = self.profiler.metrics["original_memory_test"].get(
            "memory_delta", {}
        )
        optimized_memory_delta = self.profiler.metrics["optimized_memory_test"].get(
            "memory_delta", {}
        )

        original_memory = (
            original_memory_delta.get("rss_mb", 0)
            if isinstance(original_memory_delta, dict)
            else 0
        )
        optimized_memory = (
            optimized_memory_delta.get("rss_mb", 0)
            if isinstance(optimized_memory_delta, dict)
            else 0
        )

        print(f"[bold blue]Memory Usage:[/bold blue]")
        print(f"  Original memory delta: {original_memory:.2f} MB")
        print(f"  Optimized memory delta: {optimized_memory:.2f} MB")

        # Memory usage should be similar or better (allowing for some variance)
        memory_ratio = (
            abs(optimized_memory) / abs(original_memory) if original_memory != 0 else 1
        )
        print(f"  Memory efficiency ratio: {memory_ratio:.2f}")

        print("[bold green]✓ Memory efficiency test completed[/bold green]")

    def test_data_cleaning_optimization(self):
        """Test optimized data cleaning functions."""
        print("\n[bold blue]Testing Data Cleaning Optimization...[/bold blue]")

        # Test data cleaning functions
        original_cleaned = clean_dataframe(self.sample_data.copy())
        optimized_cleaned = clean_dataframe_optimized(self.sample_data.copy())

        # Compare cleaned data
        self.assertEqual(
            original_cleaned.shape,
            optimized_cleaned.shape,
            "Cleaned data should have same shape",
        )

        # Test specific cleaning operations
        for col in ["healthStationName", "countryId", "campaignName", "clientName"]:
            if col in original_cleaned.columns and col in optimized_cleaned.columns:
                # Both should remove quotes
                self.assertFalse(
                    any(original_cleaned[col].str.contains('"', na=False)),
                    f"Original cleaning should remove quotes from {col}",
                )
                self.assertFalse(
                    any(optimized_cleaned[col].str.contains('"', na=False)),
                    f"Optimized cleaning should remove quotes from {col}",
                )

        print("[bold green]✓ Data cleaning optimization test passed[/bold green]")

    def test_caching_functionality(self):
        """Test caching mechanisms."""
        print("\n[bold blue]Testing Caching Functionality...[/bold blue]")

        from src.cache_manager import cache_manager

        # Clear cache first
        cache_manager.clear_all()

        # Test basic caching
        test_key = "test_function"
        test_data = {"test": "data", "number": 42}

        # Set cache
        cache_manager.set(test_key, test_data, use_disk=False)

        # Get from cache
        cached_data = cache_manager.get(test_key, use_disk=False)

        self.assertEqual(cached_data, test_data, "Cached data should match original")

        # Test cache stats
        stats = cache_manager.get_stats()
        self.assertGreater(stats["hits"], 0, "Should have cache hits")

        print(f"[bold blue]Cache Stats:[/bold blue] {stats}")
        print("[bold green]✓ Caching functionality test passed[/bold green]")

    def test_json_parsing_optimization(self):
        """Test optimized JSON parsing."""
        print("\n[bold blue]Testing JSON Parsing Optimization...[/bold blue]")

        from src.optimized_processors import parse_json_vectorized

        # Create test JSON data
        json_data = [
            '{"test": "value1", "number": 1}',
            '{"test": "value2", "number": 2}',
            '{"test": "value3", "number": 3}',
            "invalid_json",
            None,
            "",
        ]

        json_series = pd.Series(json_data)
        parsed_data = parse_json_vectorized(json_series)

        # Check that valid JSON was parsed correctly
        self.assertEqual(parsed_data[0]["test"], "value1")
        self.assertEqual(parsed_data[1]["number"], 2)

        # Check that invalid JSON returns empty dict
        self.assertEqual(parsed_data[3], {})
        self.assertEqual(parsed_data[4], {})
        self.assertEqual(parsed_data[5], {})

        print("[bold green]✓ JSON parsing optimization test passed[/bold green]")

    def test_batch_processing(self):
        """Test batch processing functionality."""
        print("\n[bold blue]Testing Batch Processing...[/bold blue]")

        from src.optimized_helpers import batch_process_dataframe

        # Create large dataset
        large_data = pd.concat([self.sample_data] * 5, ignore_index=True)

        def dummy_process_func(df, multiplier=1):
            # Simple processing function for testing
            result = df.copy()
            result["processed"] = True
            result["batch_size"] = len(df) * multiplier
            return result

        # Test batch processing
        result = batch_process_dataframe(
            large_data, batch_size=100, process_func=dummy_process_func, multiplier=2
        )

        # Verify results
        self.assertEqual(
            len(result), len(large_data), "Batch processing should preserve row count"
        )
        self.assertTrue(all(result["processed"]), "All rows should be processed")

        print("[bold green]✓ Batch processing test passed[/bold green]")

    def run_comprehensive_test(self):
        """Run all tests and provide comprehensive report."""
        print("\n[bold green]🧪 Running Comprehensive Optimization Tests[/bold green]")

        test_methods = [
            self.test_functionality_equivalence,
            self.test_performance_improvement,
            self.test_memory_efficiency,
            self.test_data_cleaning_optimization,
            self.test_caching_functionality,
            self.test_json_parsing_optimization,
            self.test_batch_processing,
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_method in test_methods:
            try:
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"[bold red]❌ {test_method.__name__} failed: {e}[/bold red]")

        print(f"\n[bold blue]Test Summary:[/bold blue]")
        print(f"  Passed: {passed_tests}/{total_tests}")
        print(f"  Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests == total_tests:
            print("[bold green]🎉 All optimization tests passed![/bold green]")
        else:
            print(
                f"[bold yellow]⚠️  {total_tests - passed_tests} tests failed[/bold yellow]"
            )

        return passed_tests == total_tests


def create_performance_comparison_report():
    """Create a detailed performance comparison report."""
    print("\n[bold blue]📊 Creating Performance Comparison Report...[/bold blue]")

    # Create test instance
    test_instance = TestOptimizations()
    test_instance.setUp()

    # Create different sized datasets for testing
    datasets = {
        "small": test_instance.sample_data,
        "medium": pd.concat([test_instance.sample_data] * 5, ignore_index=True),
        "large": pd.concat([test_instance.sample_data] * 10, ignore_index=True),
    }

    report = {
        "timestamp": pd.Timestamp.now().isoformat(),
        "datasets": {},
        "summary": {},
    }

    for size_name, dataset in datasets.items():
        print(
            f"\n[bold yellow]Testing {size_name} dataset ({len(dataset)} records)...[/bold yellow]"
        )

        # Benchmark functions
        functions_to_test = [
            (
                "original",
                create_custom_question_response_df_from_df,
                (dataset, 350),
                {},
            ),
            (
                "optimized",
                create_custom_question_response_df_optimized,
                (dataset, 350),
                {},
            ),
        ]

        results = benchmark_functions(functions_to_test, iterations=3)

        original_time = results["original"]["average_time"]
        optimized_time = results["optimized"]["average_time"]
        improvement = ((original_time - optimized_time) / original_time) * 100

        report["datasets"][size_name] = {
            "record_count": len(dataset),
            "original_time": original_time,
            "optimized_time": optimized_time,
            "improvement_percent": round(improvement, 1),
            "speedup_factor": round(original_time / optimized_time, 2),
        }

    # Calculate summary statistics
    improvements = [data["improvement_percent"] for data in report["datasets"].values()]
    speedups = [data["speedup_factor"] for data in report["datasets"].values()]

    report["summary"] = {
        "average_improvement_percent": round(np.mean(improvements), 1),
        "average_speedup_factor": round(np.mean(speedups), 2),
        "best_improvement_percent": round(max(improvements), 1),
        "best_speedup_factor": round(max(speedups), 2),
    }

    # Save report
    report_filename = (
        f"performance_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    with open(report_filename, "w") as f:
        json.dump(report, f, indent=2)

    print(f"\n[bold green]📈 Performance Report Summary:[/bold green]")
    print(f"  Average improvement: {report['summary']['average_improvement_percent']}%")
    print(f"  Average speedup: {report['summary']['average_speedup_factor']}x")
    print(f"  Best improvement: {report['summary']['best_improvement_percent']}%")
    print(f"  Best speedup: {report['summary']['best_speedup_factor']}x")
    print(f"  Report saved to: {report_filename}")

    return report


if __name__ == "__main__":
    # Run comprehensive tests
    test_suite = TestOptimizations()
    test_suite.setUp()
    success = test_suite.run_comprehensive_test()

    # Create performance report
    performance_report = create_performance_comparison_report()

    if success:
        print("\n[bold green]✅ All optimizations validated successfully![/bold green]")
    else:
        print("\n[bold red]❌ Some optimizations need attention[/bold red]")
