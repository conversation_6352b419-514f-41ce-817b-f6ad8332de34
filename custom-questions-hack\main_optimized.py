"""
Optimized version of the custom questions script with significant performance improvements.

Key optimizations:
1. Improved database query performance with larger batches and connection pooling
2. Vectorized data processing operations
3. Caching mechanisms for frequently accessed data
4. Memory-efficient batch processing
5. Performance monitoring and profiling
"""

import gc
import time
from datetime import datetime
from enum import Enum

import pandas as pd
from rich import print
from sqlalchemy import create_engine
from src.cache_manager import cache_manager
from src.config import load_settings
from src.extractors import (
    get_address_ids_from_universe,
    get_ireland_address_ids_from_universe,
    get_max_created_date_from_puddle,
    get_uk_address_ids_from_universe,
    today,
)
from src.optimized_helpers import (
    batch_process_dataframe,
    clean_dataframe_optimized,
    get_memory_usage,
    profile_function_performance,
)
from src.optimized_processors import (
    create_custom_question_response_df_optimized,
    optimize_dataframe_memory,
)
from src.writers import compile_universe_query, run_universe_query, save_to_file
from typer import Option, Typer, prompt
from typing_extensions import Annotated

app = Typer()

DONE_ADD_IDS = [4165, 1169, 4788, 4136, 3941, 4387, 4785, 2971, 4167, 3442]


class AddressScope(Enum):
    ALL = "all"
    IRELAND = "ireland"
    UK = "uk"

    def __str__(self):
        return self.value.capitalize()


class PerformanceMonitor:
    """Monitor and track performance metrics."""

    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()

    def start_timer(self, operation: str):
        self.metrics[operation] = {"start": time.time()}

    def end_timer(self, operation: str):
        if operation in self.metrics:
            self.metrics[operation]["end"] = time.time()
            self.metrics[operation]["duration"] = (
                self.metrics[operation]["end"] - self.metrics[operation]["start"]
            )

    def get_summary(self):
        total_time = time.time() - self.start_time
        summary = {"total_execution_time": round(total_time, 2), "operations": {}}

        for op, data in self.metrics.items():
            if "duration" in data:
                summary["operations"][op] = {
                    "duration": round(data["duration"], 2),
                    "percentage": round((data["duration"] / total_time) * 100, 1),
                }

        return summary


@app.command()
def main(
    universe_db_host: str = "127.0.0.1",
    universe_db_port: int = 3308,
    universe_db_name: str = "universe_api",
    puddle_db_host: str = "127.0.0.1",
    puddle_db_port: int = 3307,
    puddle_db_name: str = "puddle",
    verbose: bool = True,
    test: Annotated[bool, Option("--test", "-t", help="Run in test mode")] = False,
    enable_caching: Annotated[
        bool, Option("--cache", help="Enable caching for better performance")
    ] = True,
    batch_size: Annotated[
        int, Option("--batch-size", help="Batch size for processing")
    ] = 50,
):
    """
    Optimized main function with performance improvements.
    """
    perf_monitor = PerformanceMonitor()
    perf_monitor.start_timer("total_execution")

    try:
        print("[bold green]🚀 Starting Optimized Custom Questions Script[/bold green]")

        # Initialize caching if enabled
        if enable_caching:
            print(
                "[bold blue]Info: [/bold blue] Caching enabled for better performance"
            )
        else:
            cache_manager.clear_all()

        # Database setup
        perf_monitor.start_timer("database_setup")

        if test:
            db_pass = "+yXqn4]whm$Vp}Mh"
        else:
            db_pass: str = prompt(
                "Enter password for both Universe & PuddleDB user-'reporting'",
                type=str,
                default="+yXqn4]whm$Vp}Mh",
                hide_input=True,
                show_default=False,
            )

        # Set/Load Configs
        settings = load_settings(
            db_pass,
            universe_db_host,
            universe_db_port,
            universe_db_name,
            puddle_db_host,
            puddle_db_port,
            puddle_db_name,
            verbose,
        )

        # Create SQL Engine with optimized settings
        puddle_sql_engine = create_engine(
            settings.puddle_db_conn_str,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
        universe_sql_engine = create_engine(
            settings.universe_db_conn_str,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
        )

        perf_monitor.end_timer("database_setup")

        # Get max created date
        perf_monitor.start_timer("get_max_date")

        max_created_date_prompt = prompt(
            "Enter the last updated date",
            type=str,
            default="2025-05-06 09:54:19",
        )

        if not max_created_date_prompt:
            max_created_date: datetime = get_max_created_date_from_puddle(
                puddle_sql_engine,
            )
        else:
            max_created_date = datetime.strptime(
                max_created_date_prompt, "%Y-%m-%d %H:%M:%S"
            )

        print(
            "[bold blue]Data: [/bold blue] Max Created Date from Puddle: ",
            max_created_date,
        )

        perf_monitor.end_timer("get_max_date")

        # Skip and end if already done in latest
        if today <= max_created_date:
            print(
                "[bold green]Voila![/bold green]",
                "Everything is already up to date.",
            )
            return

        # Data processing logic
        use_csv = prompt(
            "Do you want to use a csv, instead of a fresh query?",
            type=bool,
            default=True,
        )

        if not use_csv:
            # Get address IDs with optimized queries
            perf_monitor.start_timer("get_address_ids")

            address_scope_choices = [scope.value for scope in AddressScope]
            text = "Which address scope do you want to query? \n"
            text += f"{address_scope_choices}"
            address_scope_str = prompt(
                text,
                type=str,
                default=AddressScope.ALL.value,
                show_choices=True,
            )

            try:
                address_scope = AddressScope(address_scope_str.lower())
            except ValueError:
                print(
                    "[bold red]Error: [/bold red]",
                    "Invalid address scope selected.",
                )
                return

            if address_scope == AddressScope.ALL:
                print(
                    "[bold green]Action: [/bold green]",
                    "Getting All Address IDs from Universe",
                )
                address_ids = get_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            elif address_scope == AddressScope.IRELAND:
                address_ids = get_ireland_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            elif address_scope == AddressScope.UK:
                address_ids = get_uk_address_ids_from_universe(
                    universe_sql_engine,
                    max_created_date.strftime("%Y-%m-%d %H:%M:%S"),
                    test,
                )
            else:
                raise ValueError("Invalid address scope selected.")

            address_ids = [
                address_id
                for address_id in address_ids
                if address_id not in DONE_ADD_IDS
            ]

            perf_monitor.end_timer("get_address_ids")

            print(
                f"[bold blue]Data: [/bold blue] Found {len(address_ids)} addresses to process"
            )

            # Compile and execute queries with optimized batch size
            perf_monitor.start_timer("query_execution")

            print("[bold yellow]Action: [/bold yellow] Compiling optimized queries")
            query_list = compile_universe_query(
                address_ids,
                max_created_date,
                batch=batch_size,
            )

            execute_permission: bool = prompt(
                "Do you want to execute the query? (Y/N)",
                type=bool,
            )

            if not execute_permission:
                print(
                    "[bold red]Alert: [/bold red]",
                    "Query execution cancelled by user.",
                )
                return

            print("[bold yellow]Action: [/bold yellow] Executing optimized queries")
            result = run_universe_query(universe_sql_engine, query_list)

            perf_monitor.end_timer("query_execution")

            if result.empty:
                print("[bold red]Alert: [/bold red] No data found")
                return

            print(f"[bold blue]Data: [/bold blue] Raw result shape: {result.shape}")
            print(f"[bold blue]Memory: [/bold blue] {get_memory_usage(result)}")

            # Clean the DataFrame with optimized functions
            perf_monitor.start_timer("data_cleaning")

            print(
                "[bold yellow]Action: [/bold yellow] Cleaning DataFrame with optimized functions"
            )
            result = clean_dataframe_optimized(result)

            # Optimize memory usage
            result = optimize_dataframe_memory(result)

            perf_monitor.end_timer("data_cleaning")

            print(f"[bold blue]Data: [/bold blue] Cleaned result shape: {result.shape}")
            print(f"[bold blue]Memory: [/bold blue] {get_memory_usage(result)}")

            # Save the result to file
            save_to_file_prompt = prompt(
                "Do you want to save the result to a file? (Y/N)",
                type=bool,
                default=True,
            )
            if save_to_file_prompt:
                default_file_path = "data/universe_query_result_optimized.csv"
                universe_query_filename = prompt(
                    "Enter the filename to save the query result",
                    type=str,
                    default=(default_file_path if test else "file_path/file_name.csv"),
                )
                save_to_file(result, universe_query_filename)

        else:
            # Load from CSV
            perf_monitor.start_timer("csv_loading")

            csv_path = prompt(
                "Enter the file path with .csv",
                type=str,
                default="data/universe_ireland.csv",
            )

            result = pd.read_csv(csv_path)
            result = optimize_dataframe_memory(result)

            perf_monitor.end_timer("csv_loading")

            print(f"[bold blue]Data: [/bold blue] Loaded CSV shape: {result.shape}")

        # Process custom questions with optimized functions
        perf_monitor.start_timer("custom_question_processing")

        surveyId = prompt(
            "Enter the surveyId to extract custom questions",
            type=int,
            default=350,
        )

        print(
            "[bold yellow]Action: [/bold yellow]",
            "Creating custom question response with optimized processing",
            f"for surveyId: {surveyId}",
        )

        # Use optimized processing function
        filtered_response = create_custom_question_response_df_optimized(
            result,
            surveyId,
        )

        perf_monitor.end_timer("custom_question_processing")

        if filtered_response.empty:
            print("[bold red]Alert: [/bold red] No custom question responses found")
            return

        print(
            f"[bold blue]Data: [/bold blue] Filtered response shape: {filtered_response.shape}"
        )
        print(f"[bold blue]Memory: [/bold blue] {get_memory_usage(filtered_response)}")

        # Upload to database
        upload_to_puddle = prompt(
            "Upload to Puddle DB?",
            type=bool,
            default=True,
        )

        if upload_to_puddle:
            perf_monitor.start_timer("database_upload")

            print(
                "[bold yellow]Action: [/bold yellow] Uploading to database with optimized chunking"
            )

            # Use larger chunk size for better performance
            chunk_size = 1000

            if surveyId in [350, 330, 38]:
                filtered_response.to_sql(
                    "Custom_Question_Responses",
                    con=puddle_sql_engine,
                    if_exists="append",
                    index=False,
                    chunksize=chunk_size,
                )
            elif surveyId == 83:
                filtered_response.to_sql(
                    "PSS4_Universe_Responses",
                    con=puddle_sql_engine,
                    if_exists="append",
                    index=False,
                    chunksize=chunk_size,
                )

            perf_monitor.end_timer("database_upload")

        # Performance summary
        perf_monitor.end_timer("total_execution")
        performance_summary = perf_monitor.get_summary()
        cache_stats = cache_manager.get_stats()

        print("\n[bold green]🎉 Performance Summary[/bold green]")
        print(
            f"[bold blue]Total Execution Time: [/bold blue] {performance_summary['total_execution_time']} seconds"
        )

        for operation, stats in performance_summary["operations"].items():
            print(
                f"[bold yellow]{operation}: [/bold yellow] {stats['duration']}s ({stats['percentage']}%)"
            )

        if enable_caching:
            print(f"\n[bold blue]Cache Performance:[/bold blue]")
            print(f"  - Cache hits: {cache_stats['hits']}")
            print(f"  - Cache misses: {cache_stats['misses']}")
            print(f"  - Hit rate: {cache_stats['hit_rate']}%")

        # Force garbage collection
        gc.collect()

    except Exception as e:
        print("[bold red]Error: [/bold red]", e)
        perf_monitor.end_timer("total_execution")
        performance_summary = perf_monitor.get_summary()
        print(
            f"Operation failed after {performance_summary['total_execution_time']} seconds"
        )
        return


if __name__ == "__main__":
    app()
