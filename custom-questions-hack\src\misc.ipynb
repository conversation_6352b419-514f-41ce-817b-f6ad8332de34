{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1b3a873f", "metadata": {}, "outputs": [], "source": ["# Define the target survey ID (this will be set dynamically)\n", "target_survey_id = 350"]}, {"cell_type": "code", "execution_count": 2, "id": "86114e32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>responseId</th>\n", "      <th>addressId</th>\n", "      <th>healthStationId</th>\n", "      <th>healthStationName</th>\n", "      <th>countryId</th>\n", "      <th>clientId</th>\n", "      <th>clientName</th>\n", "      <th>campaignId</th>\n", "      <th>campaignName</th>\n", "      <th>createdAt</th>\n", "      <th>isTest</th>\n", "      <th>survey_ids</th>\n", "      <th>questions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>23bc6102-6159-4706-98b1-421b740234e8</td>\n", "      <td>3265</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>2025-06-23 12:15:41.290000+00:00</td>\n", "      <td>False</td>\n", "      <td>[371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...</td>\n", "      <td>[[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3b4f32e8-6eee-4b25-88de-23c5d41c782e</td>\n", "      <td>3265</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>2025-06-23 17:42:27.796000+00:00</td>\n", "      <td>False</td>\n", "      <td>[371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...</td>\n", "      <td>[[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>511e8512-850b-4a2d-b947-4a0e1cea8ea9</td>\n", "      <td>3265</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>2025-06-23 18:20:35.484000+00:00</td>\n", "      <td>False</td>\n", "      <td>[371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...</td>\n", "      <td>[[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6b29fecf-34c6-4351-b717-c3490cdf7eef</td>\n", "      <td>3265</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>2025-06-23 08:47:42.549000+00:00</td>\n", "      <td>False</td>\n", "      <td>[371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...</td>\n", "      <td>[[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>70ad1218-0999-4e51-86a6-33b60cb30413</td>\n", "      <td>3265</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>2025-06-23 14:09:33.461000+00:00</td>\n", "      <td>False</td>\n", "      <td>[371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...</td>\n", "      <td>[[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             responseId  addressId  healthStationId  \\\n", "0  23bc6102-6159-4706-98b1-421b740234e8       3265              700   \n", "1  3b4f32e8-6eee-4b25-88de-23c5d41c782e       3265              700   \n", "2  511e8512-850b-4a2d-b947-4a0e1cea8ea9       3265              700   \n", "3  6b29fecf-34c6-4351-b717-c3490cdf7eef       3265              700   \n", "4  70ad1218-0999-4e51-86a6-33b60cb30413       3265              700   \n", "\n", "        healthStationName  countryId  clientId    clientName  campaignId  \\\n", "0  Cool Planet Enniskerry        108       728  Cool Planet          394   \n", "1  Cool Planet Enniskerry        108       728  Cool Planet          394   \n", "2  Cool Planet Enniskerry        108       728  Cool Planet          394   \n", "3  Cool Planet Enniskerry        108       728  Cool Planet          394   \n", "4  Cool Planet Enniskerry        108       728  Cool Planet          394   \n", "\n", "        campaignName                         createdAt  isTest  \\\n", "0  Cool Planet (VHI)  2025-06-23 12:15:41.290000+00:00   False   \n", "1  Cool Planet (VHI)  2025-06-23 17:42:27.796000+00:00   False   \n", "2  Cool Planet (VHI)  2025-06-23 18:20:35.484000+00:00   False   \n", "3  Cool Planet (VHI)  2025-06-23 08:47:42.549000+00:00   False   \n", "4  Cool Planet (VHI)  2025-06-23 14:09:33.461000+00:00   False   \n", "\n", "                                          survey_ids  \\\n", "0  [371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...   \n", "1  [371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...   \n", "2  [371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...   \n", "3  [371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...   \n", "4  [371, 372, 13, 105, 454, 350, 14, 331, 15, 83,...   \n", "\n", "                                           questions  \n", "0  [[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...  \n", "1  [[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...  \n", "2  [[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...  \n", "3  [[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...  \n", "4  [[{\"schema\": {\"code\": \"FIRST_NAME\", \"meta\": {}...  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "target_survery_id: int = 350\n", "\n", "input_df = pd.read_csv(\"../data/test_ireland.csv\")\n", "\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "73d27854", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>responseId</th>\n", "      <th>surveyId</th>\n", "      <th>healthStationId</th>\n", "      <th>healthStationInstallation</th>\n", "      <th>countryId</th>\n", "      <th>campaignId</th>\n", "      <th>campaignName</th>\n", "      <th>clientId</th>\n", "      <th>clientName</th>\n", "      <th>createdDate</th>\n", "      <th>isTest</th>\n", "      <th>questionId</th>\n", "      <th>answer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [responseId, surveyId, healthStationId, healthStationInstallation, countryId, campaignId, campaignName, clientId, clientName, createdDate, isTest, questionId, answer]\n", "Index: []"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["puddle_output_df = pd.DataFrame(\n", "    columns=[\n", "        \"responseId\",\n", "        \"surveyId\",\n", "        \"healthStationId\",\n", "        \"healthStationInstallation\",\n", "        \"countryId\",\n", "        \"campaignId\",\n", "        \"campaignName\",\n", "        \"clientId\",\n", "        \"clientName\",\n", "        \"createdDate\",\n", "        \"isTest\",\n", "        \"questionId\",\n", "        \"answer\",\n", "    ]\n", ")\n", "\n", "puddle_output_df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "8725b9f1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>responseId</th>\n", "      <th>surveyId</th>\n", "      <th>healthStationId</th>\n", "      <th>healthStationInstallation</th>\n", "      <th>countryId</th>\n", "      <th>campaignId</th>\n", "      <th>campaignName</th>\n", "      <th>clientId</th>\n", "      <th>clientName</th>\n", "      <th>createdDate</th>\n", "      <th>isTest</th>\n", "      <th>questionId</th>\n", "      <th>answer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>23bc6102-6159-4706-98b1-421b740234e8</td>\n", "      <td>350</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>2025-06-23 12:15:41.290000+00:00</td>\n", "      <td>False</td>\n", "      <td>2783</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23bc6102-6159-4706-98b1-421b740234e8</td>\n", "      <td>350</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>2025-06-23 12:15:41.290000+00:00</td>\n", "      <td>False</td>\n", "      <td>2784</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3b4f32e8-6eee-4b25-88de-23c5d41c782e</td>\n", "      <td>350</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>2025-06-23 17:42:27.796000+00:00</td>\n", "      <td>False</td>\n", "      <td>2783</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>511e8512-850b-4a2d-b947-4a0e1cea8ea9</td>\n", "      <td>350</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>2025-06-23 18:20:35.484000+00:00</td>\n", "      <td>False</td>\n", "      <td>2783</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6b29fecf-34c6-4351-b717-c3490cdf7eef</td>\n", "      <td>350</td>\n", "      <td>700</td>\n", "      <td>Cool Planet Enniskerry</td>\n", "      <td>108</td>\n", "      <td>394</td>\n", "      <td>Cool Planet (VHI)</td>\n", "      <td>728</td>\n", "      <td>Cool Planet</td>\n", "      <td>2025-06-23 08:47:42.549000+00:00</td>\n", "      <td>False</td>\n", "      <td>2783</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             responseId  surveyId  healthStationId  \\\n", "0  23bc6102-6159-4706-98b1-421b740234e8       350              700   \n", "1  23bc6102-6159-4706-98b1-421b740234e8       350              700   \n", "2  3b4f32e8-6eee-4b25-88de-23c5d41c782e       350              700   \n", "3  511e8512-850b-4a2d-b947-4a0e1cea8ea9       350              700   \n", "4  6b29fecf-34c6-4351-b717-c3490cdf7eef       350              700   \n", "\n", "  healthStationInstallation  countryId  campaignId       campaignName  \\\n", "0    Cool Planet Enniskerry        108         394  Cool Planet (VHI)   \n", "1    Cool Planet Enniskerry        108         394  Cool Planet (VHI)   \n", "2    Cool Planet Enniskerry        108         394  Cool Planet (VHI)   \n", "3    Cool Planet Enniskerry        108         394  Cool Planet (VHI)   \n", "4    Cool Planet Enniskerry        108         394  Cool Planet (VHI)   \n", "\n", "   clientId    clientName                       createdDate  isTest  \\\n", "0       728  Cool Planet   2025-06-23 12:15:41.290000+00:00   False   \n", "1       728  Cool Planet   2025-06-23 12:15:41.290000+00:00   False   \n", "2       728  Cool Planet   2025-06-23 17:42:27.796000+00:00   False   \n", "3       728  Cool Planet   2025-06-23 18:20:35.484000+00:00   False   \n", "4       728  Cool Planet   2025-06-23 08:47:42.549000+00:00   False   \n", "\n", "   questionId answer  \n", "0        2783    Yes  \n", "1        2784     No  \n", "2        2783     No  \n", "3        2783     No  \n", "4        2783    Yes  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import pandas as pd\n", "from typing import List\n", "from rich import print\n", "\n", "new_rows = []\n", "\n", "# Iterate through each row in the input DataFrame\n", "for index, row in input_df.iterrows():\n", "    response_id: str = row[\"responseId\"]\n", "    survey_ids: List = json.loads(row[\"survey_ids\"])\n", "    health_station_id: int = row[\"healthStationId\"]\n", "    health_station_installation: str = row[\"healthStationName\"]\n", "    country_id: int = row[\"countryId\"]\n", "    campaign_id: int = row[\"campaignId\"]\n", "    campaign_name: str = row[\"campaignName\"]\n", "    client_id: int = row[\"clientId\"]\n", "    client_name: str = row[\"clientName\"]\n", "    created_date: datetime = row[\"createdAt\"]\n", "    is_test: bool = row[\"isTest\"]\n", "\n", "    # Parse the JSON data in the 'questions' column\n", "    questions_data = json.loads(row[\"questions\"])\n", "\n", "    # Find the index of the target_survey_id in the survey_ids list\n", "    try:\n", "        survey_index = survey_ids.index(target_survey_id)\n", "\n", "        # Check if the index is valid for questions_data\n", "        if survey_index < len(questions_data):\n", "            # Get the question group for the target survey ID\n", "            target_question_group = questions_data[survey_index]\n", "\n", "            # Iterate through each question in the target question group\n", "            for question in target_question_group:\n", "\n", "                # Skip question is not asked to the user\n", "                if question[\"questionState\"] == \"notAsked\":\n", "                    continue\n", "\n", "                question_id = question[\"schema\"][\"surveyQuestionId\"]\n", "                # Assuming there is only one answer per question for simplicity\n", "                answer = (\n", "                    question[\"answers\"][0][\"answer\"] if question[\"answers\"] else None\n", "                )\n", "\n", "                # Create a new row dictionary\n", "                new_row = {\n", "                    \"responseId\": response_id,\n", "                    \"surveyId\": target_survey_id,  # Use the target survey ID\n", "                    \"healthStationId\": health_station_id,\n", "                    \"healthStationInstallation\": health_station_installation,\n", "                    \"countryId\": country_id,\n", "                    \"campaignId\": campaign_id,\n", "                    \"campaignName\": campaign_name,\n", "                    \"clientId\": client_id,\n", "                    \"clientName\": client_name,\n", "                    \"createdDate\": created_date,\n", "                    \"isTest\": is_test,\n", "                    \"questionId\": question_id,\n", "                    \"answer\": answer,\n", "                }\n", "\n", "                # Append the new row dictionary to the list\n", "                new_rows.append(new_row)\n", "        else:\n", "            print(\n", "                f\"Warning: Survey ID {target_survey_id} index out of bounds for questions data in row {index}\"\n", "            )\n", "\n", "    except ValueError:\n", "        print(\n", "            f\"Warning: Target survey ID {target_survey_id} not found in survey_ids for row {index}\"\n", "        )\n", "\n", "# Concatenate the list of new rows into the puddle_output_df\n", "puddle_output_df = pd.concat([pd.DataFrame(new_rows)], ignore_index=True)\n", "\n", "# Display the head of the populated puddle_output_df\n", "puddle_output_df.head()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}