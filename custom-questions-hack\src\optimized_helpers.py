"""
Optimized helper functions for better performance.
This module contains vectorized and optimized versions of the original helper functions.
"""

import re
from datetime import datetime
from typing import Tuple

import numpy as np
import pandas as pd
from dateutil import tz


def clean_text_vectorized(series: pd.Series) -> pd.Series:
    """
    Vectorized version of text cleaning.
    """
    if series.empty:
        return series

    # Use vectorized string operations
    return series.str.strip('"').str.strip("'")


def convert_utc_to_local_vectorized(series: pd.Series) -> pd.Series:
    """
    Vectorized version of UTC to local time conversion.
    """
    if series.empty:
        return series

    from_zone = tz.gettz("UTC")
    to_zone = tz.gettz("GMT")

    # Clean the series first
    cleaned_series = series.str.replace('"', "").str.replace("'", "")

    # Convert to datetime
    datetime_series = pd.to_datetime(cleaned_series, errors="coerce")

    # Localize to UTC if not already timezone-aware
    if datetime_series.dt.tz is None:
        datetime_series = datetime_series.dt.tz_localize(from_zone)

    # Convert to target timezone
    return datetime_series.dt.tz_convert(to_zone)


def clean_dataframe_optimized(df: pd.DataFrame) -> pd.DataFrame:
    """
    Optimized version of DataFrame cleaning using vectorized operations.
    """
    if df.empty:
        return df

    print("[bold yellow]Action: [/bold yellow] Optimized DataFrame cleaning")

    # Clean text columns using vectorized operations
    text_columns = ["healthStationName", "countryId", "campaignName", "clientName"]

    for col in text_columns:
        if col in df.columns:
            print(f"[bold yellow]Action: [/bold yellow] Cleaning column: {col}")
            df[col] = clean_text_vectorized(df[col])

    # Clean and convert createdAt column
    if "createdAt" in df.columns:
        print(
            "[bold yellow]Action: [/bold yellow] Converting createdAt to local timezone"
        )
        df["createdAt"] = convert_utc_to_local_vectorized(df["createdAt"])

    # Clean questions column
    if "questions" in df.columns:
        print("[bold yellow]Action: [/bold yellow] Cleaning questions column")
        df["questions"] = clean_text_vectorized(df["questions"])

    return df


def batch_process_dataframe(
    df: pd.DataFrame, batch_size: int = 10000, process_func=None, **kwargs
) -> pd.DataFrame:
    """
    Process DataFrame in batches to manage memory usage.
    """
    if df.empty or process_func is None:
        return df

    if len(df) <= batch_size:
        return process_func(df, **kwargs)

    print(
        f"[bold yellow]Action: [/bold yellow] Processing {len(df)} records in batches of {batch_size}"
    )

    results = []
    for i in range(0, len(df), batch_size):
        batch = df.iloc[i : i + batch_size]
        print(
            f"[bold yellow]Action: [/bold yellow] Processing batch {i//batch_size + 1}/{(len(df)-1)//batch_size + 1}"
        )

        batch_result = process_func(batch, **kwargs)
        if not batch_result.empty:
            results.append(batch_result)

    if not results:
        return pd.DataFrame()

    # Concatenate results efficiently
    return pd.concat(results, ignore_index=True, copy=False)


def optimize_json_parsing(json_str: str) -> dict:
    """
    Optimized JSON parsing with better error handling and performance.
    """
    if not json_str or pd.isna(json_str):
        return {}

    # Clean the string
    if isinstance(json_str, str):
        json_str = json_str.strip().strip("\"'")

    try:
        import orjson

        # Use orjson if available (much faster than standard json)
        return orjson.loads(json_str)
    except ImportError:
        # Fall back to standard json
        try:
            import json

            return json.loads(json_str)
        except json.JSONDecodeError:
            # Try to fix common JSON issues
            return autofix_json_optimized(json_str)


def autofix_json_optimized(json_str: str) -> dict:
    """
    Optimized version of JSON auto-fixing with better performance.
    """
    if not json_str:
        return {}

    try:
        import json

        return json.loads(json_str)
    except json.JSONDecodeError:
        pass

    # Apply common fixes
    fixed = json_str.strip()

    # Replace single quotes with double quotes (but not inside strings)
    fixed = re.sub(r"(?<!\\)'", '"', fixed)

    # Remove trailing commas
    fixed = re.sub(r",(\s*[}\]])", r"\1", fixed)

    # Fix multi-line strings in helpText fields
    fixed = re.sub(
        r'("helpText"\s*:\s*")((?:[^"\\]|\\.)*)"',
        lambda m: m.group(1) + re.sub(r"[\r\n]+", " ", m.group(2)) + '"',
        fixed,
        flags=re.MULTILINE,
    )

    # Clean excessive whitespace
    fixed = re.sub(r"\s+", " ", fixed)

    try:
        import json

        return json.loads(fixed)
    except json.JSONDecodeError:
        return {}


def validate_dates_optimized(from_date: str, to_date: str) -> Exception | None:
    """
    Optimized date validation.
    """
    if not (to_date and from_date):
        return None

    try:
        from_date_obj = datetime.fromisoformat(from_date)
        to_date_obj = datetime.fromisoformat(to_date)

        if to_date_obj <= from_date_obj:
            return Exception("Error in dates, please enter correct dates")
    except ValueError:
        return Exception("Invalid date format")

    return None


def memory_efficient_concat(dataframes: list, chunk_size: int = 5) -> pd.DataFrame:
    """
    Memory-efficient concatenation of DataFrames.
    """
    if not dataframes:
        return pd.DataFrame()

    if len(dataframes) == 1:
        return dataframes[0]

    # Process in chunks to avoid memory issues
    result = None

    for i in range(0, len(dataframes), chunk_size):
        chunk = dataframes[i : i + chunk_size]
        chunk_result = pd.concat(chunk, ignore_index=True, copy=False)

        if result is None:
            result = chunk_result
        else:
            result = pd.concat([result, chunk_result], ignore_index=True, copy=False)

    return result


def get_memory_usage(df: pd.DataFrame) -> dict:
    """
    Get detailed memory usage information for a DataFrame.
    """
    if df.empty:
        return {"total_mb": 0, "columns": {}}

    memory_usage = df.memory_usage(deep=True)
    total_mb = memory_usage.sum() / 1024 / 1024

    column_usage = {}
    for col in df.columns:
        col_mb = memory_usage[col] / 1024 / 1024
        column_usage[col] = {
            "mb": round(col_mb, 2),
            "dtype": str(df[col].dtype),
            "null_count": df[col].isnull().sum(),
            "unique_count": df[col].nunique(),
        }

    return {"total_mb": round(total_mb, 2), "rows": len(df), "columns": column_usage}


def profile_function_performance(func, *args, **kwargs):
    """
    Simple function performance profiler.
    """
    import time

    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()

    execution_time = end_time - start_time

    print(
        f"[bold blue]Performance: [/bold blue] {func.__name__} took {execution_time:.2f} seconds"
    )

    return result, execution_time
