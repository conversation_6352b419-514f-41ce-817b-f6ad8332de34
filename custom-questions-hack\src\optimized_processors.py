"""
Optimized data processing functions for better performance.
This module contains vectorized and optimized versions of the original processing functions.
"""

import json
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import partial
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd


def parse_json_vectorized(json_series: pd.Series) -> List[Dict[str, Any]]:
    """
    Optimized JSON parsing with adaptive strategy based on dataset size.
    """

    def safe_json_loads(json_str):
        try:
            if pd.isna(json_str) or json_str == "":
                return {}
            # Clean the JSON string
            if isinstance(json_str, str):
                json_str = json_str.strip("\"'")
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError):
            return {}

    # Use multiprocessing only for very large datasets (>5000 records)
    # For smaller datasets, the overhead isn't worth it
    if len(json_series) > 5000:
        try:
            with mp.<PERSON>(processes=min(mp.cpu_count(), 4)) as pool:
                parsed_data = pool.map(safe_json_loads, json_series.tolist())
        except Exception:
            # Fall back to sequential processing if multiprocessing fails
            parsed_data = [safe_json_loads(x) for x in json_series]
    else:
        # Use list comprehension for smaller datasets (faster due to no overhead)
        parsed_data = [safe_json_loads(x) for x in json_series]

    return parsed_data


def extract_survey_data_vectorized(
    df: pd.DataFrame, target_survey_id: int
) -> pd.DataFrame:
    """
    Optimized vectorized version of survey data extraction.
    Uses numpy operations and avoids row-by-row iteration.
    """
    if df.empty:
        return pd.DataFrame()

    # Parse JSON data in batches
    print("[bold yellow]Action: [/bold yellow] Parsing survey IDs...")
    survey_ids_parsed = parse_json_vectorized(df["survey_ids"])

    print("[bold yellow]Action: [/bold yellow] Parsing questions data...")
    questions_parsed = parse_json_vectorized(df["questions"])

    # Create boolean mask for rows containing target survey ID
    has_target_survey = []
    survey_indices = []

    for i, survey_list in enumerate(survey_ids_parsed):
        if isinstance(survey_list, list) and target_survey_id in survey_list:
            has_target_survey.append(True)
            survey_indices.append(survey_list.index(target_survey_id))
        else:
            has_target_survey.append(False)
            survey_indices.append(-1)

    # Filter DataFrame to only relevant rows
    mask = np.array(has_target_survey)
    if not mask.any():
        return pd.DataFrame()

    filtered_df = df[mask].copy()
    filtered_questions = [questions_parsed[i] for i in np.where(mask)[0]]
    filtered_indices = [survey_indices[i] for i in np.where(mask)[0]]

    # Process questions in batches
    all_responses = []

    for idx, (_, row) in enumerate(filtered_df.iterrows()):
        survey_idx = filtered_indices[idx]
        questions_data = filtered_questions[idx]

        if not isinstance(questions_data, list) or survey_idx >= len(questions_data):
            continue

        target_questions = questions_data[survey_idx]
        if not isinstance(target_questions, list):
            continue

        # Process all questions for this response at once
        response_data = extract_response_data(row, target_survey_id, target_questions)
        all_responses.extend(response_data)

    # Create DataFrame from all responses at once
    if not all_responses:
        return pd.DataFrame()

    return pd.DataFrame(all_responses)


def extract_response_data(
    row: pd.Series, target_survey_id: int, questions: List[Dict]
) -> List[Dict]:
    """
    Extract response data for a single row with all its questions.
    """
    base_data = {
        "responseId": row["responseId"],
        "surveyId": target_survey_id,
        "healthStationId": row["healthStationId"],
        "healthStationInstallation": row["healthStationName"],
        "countryId": row["countryId"],
        "campaignId": row["campaignId"],
        "campaignName": row["campaignName"],
        "clientId": row["clientId"],
        "clientName": row["clientName"],
        "createdDate": row["createdAt"],
        "isTest": row["isTest"],
    }

    responses = []
    for question in questions:
        if not isinstance(question, dict):
            continue

        # Skip questions not asked to the user
        if question.get("questionState") == "notAsked":
            continue

        schema = question.get("schema", {})
        question_id = schema.get("surveyQuestionId")

        if question_id is None:
            continue

        # Extract answer
        answers = question.get("answers", [])
        answer = answers[0].get("answer") if answers else None

        # Create response record
        response_record = base_data.copy()
        response_record.update(
            {
                "questionId": question_id,
                "answer": answer,
            }
        )

        responses.append(response_record)

    return responses


def create_custom_question_response_df_optimized(
    df: pd.DataFrame,
    target_survey_id: int,
) -> pd.DataFrame:
    """
    Optimized version focusing on algorithmic improvements without overhead.
    """
    if df.empty:
        return pd.DataFrame()

    print(
        f"[bold yellow]Action: [/bold yellow] Processing {len(df)} records for survey ID {target_survey_id}"
    )

    # Use simple optimized approach that avoids overhead
    return create_custom_question_response_simple_optimized(df, target_survey_id)


def extract_survey_data_vectorized_simple(
    df: pd.DataFrame, target_survey_id: int
) -> pd.DataFrame:
    """
    Simplified vectorized extraction for survey ID 83.
    """
    # Parse JSON data
    survey_ids_parsed = parse_json_vectorized(df["survey_ids"])
    questions_parsed = parse_json_vectorized(df["questions"])

    all_responses = []

    for idx, (_, row) in enumerate(df.iterrows()):
        survey_list = survey_ids_parsed[idx]
        questions_data = questions_parsed[idx]

        if not isinstance(survey_list, list) or target_survey_id not in survey_list:
            continue

        survey_idx = survey_list.index(target_survey_id)

        if not isinstance(questions_data, list) or survey_idx >= len(questions_data):
            continue

        target_questions = questions_data[survey_idx]
        if not isinstance(target_questions, list):
            continue

        # Process questions for survey ID 83
        for question in target_questions:
            if not isinstance(question, dict):
                continue

            if question.get("questionState") == "notAsked":
                continue

            schema = question.get("schema", {})
            question_id = schema.get("surveyQuestionId")

            if question_id is None:
                continue

            answers = question.get("answers", [])
            answer = answers[0].get("answer") if answers else None

            all_responses.append(
                {
                    "responseId": row["responseId"],
                    "questionId": question_id,
                    "answer": answer,
                }
            )

    return pd.DataFrame(all_responses) if all_responses else pd.DataFrame()


def optimize_dataframe_memory(df: pd.DataFrame) -> pd.DataFrame:
    """
    Optimize DataFrame memory usage by converting data types.
    """
    if df.empty:
        return df

    # Convert object columns to category where appropriate
    for col in df.select_dtypes(include=["object"]).columns:
        if df[col].nunique() / len(df) < 0.5:  # If less than 50% unique values
            df[col] = df[col].astype("category")

    # Optimize integer columns
    for col in df.select_dtypes(include=["int64"]).columns:
        col_min = df[col].min()
        col_max = df[col].max()

        if col_min >= 0:
            if col_max < 255:
                df[col] = df[col].astype("uint8")
            elif col_max < 65535:
                df[col] = df[col].astype("uint16")
            elif col_max < 4294967295:
                df[col] = df[col].astype("uint32")
        else:
            if col_min > -128 and col_max < 127:
                df[col] = df[col].astype("int8")
            elif col_min > -32768 and col_max < 32767:
                df[col] = df[col].astype("int16")
            elif col_min > -2147483648 and col_max < 2147483647:
                df[col] = df[col].astype("int32")

    return df


def create_custom_question_response_simple_optimized(
    df: pd.DataFrame,
    target_survey_id: int,
) -> pd.DataFrame:
    """
    Simple optimized version focusing on algorithmic improvements without overhead.
    """
    if df.empty:
        return pd.DataFrame()

    all_responses = []

    # Pre-compile the output columns based on survey ID
    if target_survey_id == 83:
        output_columns = ["responseId", "questionId", "answer"]
    else:
        output_columns = [
            "responseId", "surveyId", "healthStationId", "healthStationInstallation",
            "countryId", "campaignId", "campaignName", "clientId", "clientName",
            "createdDate", "isTest", "questionId", "answer"
        ]

    # Process rows more efficiently
    for _, row in df.iterrows():
        try:
            # Parse JSON once per row
            survey_ids = json.loads(row["survey_ids"])
            questions_data = json.loads(row["questions"])

            if not isinstance(survey_ids, list) or target_survey_id not in survey_ids:
                continue

            survey_index = survey_ids.index(target_survey_id)

            if survey_index >= len(questions_data):
                continue

            target_questions = questions_data[survey_index]
            if not isinstance(target_questions, list):
                continue

            # Pre-build base record to avoid repeated dictionary creation
            if target_survey_id != 83:
                base_record = {
                    "responseId": row["responseId"],
                    "surveyId": target_survey_id,
                    "healthStationId": row["healthStationId"],
                    "healthStationInstallation": row["healthStationName"],
                    "countryId": row["countryId"],
                    "campaignId": row["campaignId"],
                    "campaignName": row["campaignName"],
                    "clientId": row["clientId"],
                    "clientName": row["clientName"],
                    "createdDate": row["createdAt"],
                    "isTest": row["isTest"],
                }
            else:
                base_record = {"responseId": row["responseId"]}

            # Process questions efficiently
            for question in target_questions:
                if not isinstance(question, dict) or question.get("questionState") == "notAsked":
                    continue

                schema = question.get("schema", {})
                question_id = schema.get("surveyQuestionId")

                if question_id is None:
                    continue

                answers = question.get("answers", [])
                answer = answers[0].get("answer") if answers else None

                # Create response record efficiently
                response_record = base_record.copy()
                response_record.update({
                    "questionId": question_id,
                    "answer": answer,
                })

                all_responses.append(response_record)

        except (json.JSONDecodeError, KeyError, IndexError, TypeError):
            # Skip malformed records
            continue

    # Create DataFrame efficiently
    if not all_responses:
        return pd.DataFrame(columns=output_columns)

    result_df = pd.DataFrame(all_responses)

    # Ensure column order
    result_df = result_df.reindex(columns=output_columns, fill_value=None)

    return result_df
