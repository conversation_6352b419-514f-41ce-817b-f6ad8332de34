import ctypes
from enum import Enum


class LogLevel(Enum):
    info = "INFO"
    debug = "DEBUG"
    error = "ERROR"


class Settings:
    db_pass: str
    universe_db_conn_str: str
    puddle_db_conn_str: str
    verbose: bool
    file_size_limit: int

    def __init__(
        self,
        db_pass: str,
        universe_db_conn_str: str,
        puddle_db_conn_str: str,
        verbose: bool = True,
    ):
        self.file_size_limit = int(ctypes.c_ulong(-1).value // 2)
        self.verbose = verbose
        self.db_pass = db_pass
        self.universe_db_conn_str = universe_db_conn_str
        self.puddle_db_conn_str = puddle_db_conn_str


def load_settings(
    db_pass,
    universe_db_host,
    universe_db_port,
    universe_db_name,
    puddle_db_host,
    puddle_db_port,
    puddle_db_name,
    verbose,
):
    return Settings(
        db_pass=db_pass,
        universe_db_conn_str=(
            f"mysql+pymysql://reporting:{db_pass}"
            + f"@{universe_db_host}:{universe_db_port}/{universe_db_name}"
            + "?charset=utf8"
        ),
        puddle_db_conn_str=f"mysql+pymysql://reporting:{db_pass}"
        + f"@{puddle_db_host}:{puddle_db_port}/{puddle_db_name}"
        + "?charset=utf8",
        verbose=verbose,
    )
